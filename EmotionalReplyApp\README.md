# 情感回复助手 App

## 项目简介

这是一个基于 uni-app + 原生插件混合开发的情感智能应用，帮助用户生成合适的回复消息。

### 核心功能
- 📝 消息粘贴和分析
- 🧠 智能情感识别
- 💬 多风格回复生成
- 📋 一键复制回复
- 📚 历史记录管理

### 使用流程
1. 复制收到的消息
2. 在app中粘贴
3. 系统分析并生成回复建议
4. 选择合适的回复
5. 一键复制使用

## 项目结构

```
EmotionalReplyApp/
├── uniapp-frontend/     # uni-app跨平台应用
│   ├── pages/          # 页面文件
│   ├── components/     # 组件文件
│   ├── api/           # API接口
│   ├── store/         # 状态管理
│   ├── utils/         # 工具函数
│   └── native-plugins/ # 原生插件(悬浮窗等)
├── backend-service/     # 后端服务(Spring Boot)
├── deploy/             # 部署脚本和配置
├── docs/                # 项目文档
├── 项目设计文档.md      # 详细设计文档
└── README.md           # 项目说明
```

## 开发环境

### 前端开发 (uni-app)
- HBuilderX 或 VS Code
- Node.js 16+
- uni-app CLI
- Android Studio (用于原生插件开发)

### 后端开发
- IntelliJ IDEA 或 Eclipse
- Java 8+
- Spring Boot 2.x
- Maven 3.x
- MySQL 8.0+

## 快速开始

### 前端应用
1. 进入前端目录：`cd uniapp-frontend`
2. 安装依赖：`npm install`
3. 启动 H5 调试：`npm run dev:h5`
4. 或在 HBuilderX 中打开项目运行

### 后端服务
1. 进入后端目录：`cd backend-service`
2. 配置数据库连接
3. 运行：`mvn spring-boot:run`

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue。
