/**
 * 网络请求封装
 */

// 基础配置
const BASE_URL = 'http://localhost:8080/api'
const TIMEOUT = 10000

// 请求拦截器
const requestInterceptor = (config) => {
  // 添加 token
  const token = uni.getStorageSync('token')
  if (token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${token}`
    }
  }
  
  // 添加通用请求头
  config.header = {
    'Content-Type': 'application/json',
    ...config.header
  }

  return config
}

// 响应拦截器
const responseInterceptor = (response) => {
  const { data, statusCode } = response
  
  // HTTP 状态码检查
  if (statusCode !== 200) {
    handleHttpError(statusCode, data)
    return Promise.reject(response)
  }
  
  // 业务状态码检查
  if (data.code !== 200) {
    handleBusinessError(data.code, data.message)
    return Promise.reject(data)
  }
  
  return data.data
}

// HTTP 错误处理
const handleHttpError = (statusCode, data) => {
  let message = '网络请求失败'
  
  switch (statusCode) {
    case 401:
      message = '登录已过期，请重新登录'
      // 清除本地存储的用户信息
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      // 跳转到登录页
      uni.navigateTo({
        url: '/pages/login/login'
      })
      break
    case 403:
      message = '没有权限访问'
      break
    case 404:
      message = '请求的资源不存在'
      break
    case 500:
      message = '服务器内部错误'
      break
    default:
      message = data?.message || `请求失败 (${statusCode})`
  }
  
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  })
}

// 业务错误处理
const handleBusinessError = (code, message) => {
  uni.showToast({
    title: message || '操作失败',
    icon: 'none',
    duration: 2000
  })
}

// 封装请求方法
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 应用请求拦截器
    const config = requestInterceptor({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: options.header || {},
      timeout: options.timeout || TIMEOUT
    })
    
    uni.request({
      ...config,
      success: (response) => {
        try {
          const result = responseInterceptor(response)
          resolve(result)
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        uni.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

// GET 请求
export const get = (url, params = {}) => {
  return request({
    url: url + (Object.keys(params).length ? '?' + new URLSearchParams(params).toString() : ''),
    method: 'GET'
  })
}

// POST 请求
export const post = (url, data = {}) => {
  return request({
    url,
    method: 'POST',
    data
  })
}

// PUT 请求
export const put = (url, data = {}) => {
  return request({
    url,
    method: 'PUT',
    data
  })
}

// DELETE 请求
export const del = (url, params = {}) => {
  return request({
    url: url + (Object.keys(params).length ? '?' + new URLSearchParams(params).toString() : ''),
    method: 'DELETE'
  })
}

// 文件上传
export const upload = (url, filePath, formData = {}) => {
  return new Promise((resolve, reject) => {
    const token = uni.getStorageSync('token')
    
    uni.uploadFile({
      url: BASE_URL + url,
      filePath,
      name: 'file',
      formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (response) => {
        try {
          const data = JSON.parse(response.data)
          if (data.code === 200) {
            resolve(data.data)
          } else {
            handleBusinessError(data.code, data.message)
            reject(data)
          }
        } catch (error) {
          reject(error)
        }
      },
      fail: (error) => {
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(error)
      }
    })
  })
}

export default {
  get,
  post,
  put,
  del,
  upload
}
