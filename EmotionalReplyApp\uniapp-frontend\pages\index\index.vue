<template>
  <view class="page">
    <text class="title">🎉 情感回复助手</text>
    <text class="subtitle">智能分析，贴心回复</text>
    
    <view class="user-info">
      <text class="user-name">{{ nickname }}</text>
      <text class="quota" v-if="isLoggedIn">今日剩余: {{ remainingQuota }}/{{ dailyQuota }}</text>
      <text class="quota" v-else @click="goToLogin">点击登录以使用完整功能</text>
    </view>
    
    <view class="actions">
      <button class="btn" @click="goToMessage" :disabled="!isLoggedIn">💬 智能回复</button>
      <button class="btn" @click="goToHistory" :disabled="!isLoggedIn">📚 历史记录</button>
    </view>

    <!-- 未登录提示 -->
    <view class="login-prompt" v-if="!isLoggedIn">
      <text class="prompt-text">登录后可使用智能回复功能</text>
      <button class="login-btn" @click="goToLogin">立即登录</button>
    </view>
    
    <view class="stats-card">
      <text class="card-title">使用统计</text>
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number">{{ todayUsage }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ totalUsage }}</text>
          <text class="stat-label">总计使用</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { HistoryManager, SettingsManager } from '../../utils/storage.js'
import { getUserStats } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'

export default {
  name: 'IndexPage',
  
  data() {
    return {
      userInfo: null,
      userStats: {
        todayUsage: 0,
        totalUsage: 0,
        dailyQuota: 10,
        favoriteCount: 0
      }
    }
  },
  
  computed: {
    nickname() {
      if (!this.userInfo) {
        return '未登录'
      }
      return this.userInfo.nickname || this.userInfo.username || '用户'
    },

    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    },
    remainingQuota() {
      return Math.max(0, this.userStats.dailyQuota - this.userStats.todayUsage)
    },
    dailyQuota() {
      return this.userStats.dailyQuota
    },
    todayUsage() {
      return this.userStats.todayUsage
    },
    totalUsage() {
      return this.userStats.totalUsage
    }
  },
  
  onLoad() {
    // 路由守卫检查
    if (!AuthGuard.pageGuard('pages/index/index')) {
      return
    }

    this.initUserInfo()
    this.loadUserStats()
  },
  
  onShow() {
    // 每次显示页面时刷新用户信息和统计数据
    this.initUserInfo()
    this.loadUserStats()
  },
  
  methods: {
    // 初始化用户信息
    initUserInfo() {
      // 获取用户信息（不自动初始化测试用户）
      this.userInfo = UserManager.getUserInfo()

      // 如果未登录，显示相应状态
      if (!this.userInfo) {
      }
    },

    // 加载用户统计数据
    async loadUserStats() {
      // 如果未登录，显示默认统计
      if (!this.isLoggedIn) {
        this.userStats = {
          todayUsage: 0,
          totalUsage: 0,
          dailyQuota: 0,
          favoriteCount: 0
        }
        return
      }

      try {
        // 尝试从API获取统计数据
        const currentUserId = UserManager.getCurrentUserId()
        const stats = await getUserStats(currentUserId)
        this.userStats = {
          todayUsage: stats.todayUsage || 0,
          totalUsage: stats.totalUsage || 0,
          dailyQuota: stats.dailyQuota || 10,
          favoriteCount: 0
        }
      } catch (error) {
        // API调用失败时使用本地存储数据
        const history = HistoryManager.getHistory()
        const settings = SettingsManager.getSettings()

        // 计算今日使用量
        const today = new Date().toDateString()
        const todayHistory = history.filter(item => {
          const itemDate = new Date(item.createTime).toDateString()
          return itemDate === today
        })

        this.userStats = {
          todayUsage: todayHistory.length,
          totalUsage: history.length,
          dailyQuota: settings.dailyQuota || 10,
          favoriteCount: history.filter(item => item.isFavorite).length
        }
      }
    },
    
    goToMessage() {
      if (!this.isLoggedIn) {
        this.goToLogin()
        return
      }
      uni.navigateTo({
        url: '/pages/message/input'
      })
    },

    goToHistory() {
      if (!this.isLoggedIn) {
        this.goToLogin()
        return
      }
      uni.navigateTo({
        url: '/pages/history/history'
      })
    },

    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    }
  }
}
</script>

<style scoped>
.page {
  padding: 40rpx;
  background: #f8f9fa;
  min-height: 100vh;
  text-align: center;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.user-info {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.user-name {
  display: block;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.quota {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.btn {
  flex: 1;
  background: white;
  border: none;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  box-shadow: none;
}

.login-prompt {
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.prompt-text {
  display: block;
  color: #856404;
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.login-btn {
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 20rpx 40rpx;
  font-size: 26rpx;
}

.stats-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-row {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}
</style>
