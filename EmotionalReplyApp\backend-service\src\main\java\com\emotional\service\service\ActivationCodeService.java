package com.emotional.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.emotional.service.entity.ActivationCode;

/**
 * 激活码服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface ActivationCodeService extends IService<ActivationCode> {
    
    /**
     * 生成激活码
     * 
     * @param codeType 激活码类型
     * @param durationDays 有效天数
     * @param createdBy 创建者ID
     * @param remark 备注
     * @return 激活码
     */
    ActivationCode generateActivationCode(String codeType, Integer durationDays, Long createdBy, String remark);
    
    /**
     * 使用激活码
     * 
     * @param code 激活码
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean useActivationCode(String code, Long userId);
    
    /**
     * 检查激活码是否有效
     * 
     * @param code 激活码
     * @return 是否有效
     */
    boolean isValidCode(String code);
}
