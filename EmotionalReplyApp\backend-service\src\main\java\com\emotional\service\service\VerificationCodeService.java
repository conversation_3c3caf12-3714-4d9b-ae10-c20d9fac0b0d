package com.emotional.service.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务 - 基于Redis实现
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
@Slf4j
@Service
public class VerificationCodeService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // Redis key前缀
    private static final String REGISTER_CODE_PREFIX = "verification:register:";
    private static final String RESET_CODE_PREFIX = "verification:reset:";
    
    // 验证码有效期（分钟）
    private static final int CODE_EXPIRE_MINUTES = 5;
    
    // 验证码长度
    private static final int CODE_LENGTH = 6;

    /**
     * 生成并存储注册验证码
     * 
     * @param email 邮箱地址
     * @return 验证码
     */
    public String generateRegisterCode(String email) {
        String code = generateCode();
        String key = REGISTER_CODE_PREFIX + email;
        
        // 创建验证码信息
        VerificationCodeInfo codeInfo = new VerificationCodeInfo();
        codeInfo.setCode(code);
        codeInfo.setEmail(email);
        codeInfo.setType("register");
        codeInfo.setCreateTime(LocalDateTime.now());
        codeInfo.setExpireTime(LocalDateTime.now().plusMinutes(CODE_EXPIRE_MINUTES));
        codeInfo.setUsed(false);
        
        // 存储到Redis，设置过期时间
        redisTemplate.opsForValue().set(key, codeInfo, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        log.info("生成注册验证码: email={}, code={}, expireTime={}", 
                email, code, codeInfo.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        return code;
    }

    /**
     * 验证注册验证码
     * 
     * @param email 邮箱地址
     * @param code 验证码
     * @return 验证结果
     */
    public boolean verifyRegisterCode(String email, String code) {
        String key = REGISTER_CODE_PREFIX + email;
        
        VerificationCodeInfo codeInfo = (VerificationCodeInfo) redisTemplate.opsForValue().get(key);
        
        if (codeInfo == null) {
            log.warn("注册验证码不存在或已过期: email={}", email);
            return false;
        }
        
        if (codeInfo.isUsed()) {
            log.warn("注册验证码已被使用: email={}", email);
            return false;
        }

        // 手动检查过期时间（双重保险）
        if (LocalDateTime.now().isAfter(codeInfo.getExpireTime())) {
            log.warn("注册验证码已过期: email={}, expireTime={}", email,
                    codeInfo.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            redisTemplate.delete(key);
            return false;
        }

        if (!code.equals(codeInfo.getCode())) {
            log.warn("注册验证码错误: email={}, expected={}, actual={}", email, codeInfo.getCode(), code);
            return false;
        }
        
        // 标记为已使用
        codeInfo.setUsed(true);
        redisTemplate.opsForValue().set(key, codeInfo, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        log.info("注册验证码验证成功: email={}", email);
        return true;
    }

    /**
     * 生成并存储重置密码验证码
     * 
     * @param identifier 用户标识（邮箱）
     * @return 验证码
     */
    public String generateResetCode(String identifier) {
        String code = generateCode();
        String key = RESET_CODE_PREFIX + identifier;
        
        // 创建验证码信息
        VerificationCodeInfo codeInfo = new VerificationCodeInfo();
        codeInfo.setCode(code);
        codeInfo.setEmail(identifier);
        codeInfo.setType("reset");
        codeInfo.setCreateTime(LocalDateTime.now());
        codeInfo.setExpireTime(LocalDateTime.now().plusMinutes(CODE_EXPIRE_MINUTES));
        codeInfo.setUsed(false);
        
        // 存储到Redis，设置过期时间
        redisTemplate.opsForValue().set(key, codeInfo, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        log.info("生成重置密码验证码: identifier={}, code={}, expireTime={}", 
                identifier, code, codeInfo.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        return code;
    }

    /**
     * 验证重置密码验证码
     * 
     * @param identifier 用户标识
     * @param code 验证码
     * @return 验证结果
     */
    public boolean verifyResetCode(String identifier, String code) {
        String key = RESET_CODE_PREFIX + identifier;
        
        VerificationCodeInfo codeInfo = (VerificationCodeInfo) redisTemplate.opsForValue().get(key);
        
        if (codeInfo == null) {
            log.warn("重置密码验证码不存在或已过期: identifier={}", identifier);
            return false;
        }
        
        if (codeInfo.isUsed()) {
            log.warn("重置密码验证码已被使用: identifier={}", identifier);
            return false;
        }

        // 手动检查过期时间（双重保险）
        if (LocalDateTime.now().isAfter(codeInfo.getExpireTime())) {
            log.warn("重置密码验证码已过期: identifier={}, expireTime={}", identifier,
                    codeInfo.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            redisTemplate.delete(key);
            return false;
        }

        if (!code.equals(codeInfo.getCode())) {
            log.warn("重置密码验证码错误: identifier={}, expected={}, actual={}", identifier, codeInfo.getCode(), code);
            return false;
        }
        
        // 标记为已使用
        codeInfo.setUsed(true);
        redisTemplate.opsForValue().set(key, codeInfo, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        log.info("重置密码验证码验证成功: identifier={}", identifier);
        return true;
    }

    /**
     * 删除验证码
     * 
     * @param email 邮箱地址
     * @param type 验证码类型 (register/reset)
     */
    public void deleteCode(String email, String type) {
        String key = "register".equals(type) ? REGISTER_CODE_PREFIX + email : RESET_CODE_PREFIX + email;
        redisTemplate.delete(key);
        log.info("删除验证码: email={}, type={}", email, type);
    }

    /**
     * 生成随机验证码
     * 
     * @return 6位数字验证码
     */
    private String generateCode() {
        return String.format("%0" + CODE_LENGTH + "d", (int) (Math.random() * Math.pow(10, CODE_LENGTH)));
    }

    /**
     * 验证码信息类
     */
    public static class VerificationCodeInfo {
        private String code;
        private String email;
        private String type;
        private LocalDateTime createTime;
        private LocalDateTime expireTime;
        private boolean used;

        // Getters and Setters
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
        
        public LocalDateTime getExpireTime() { return expireTime; }
        public void setExpireTime(LocalDateTime expireTime) { this.expireTime = expireTime; }
        
        public boolean isUsed() { return used; }
        public void setUsed(boolean used) { this.used = used; }
    }
}
