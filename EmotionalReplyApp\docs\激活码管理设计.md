# 激活码管理系统设计

## 激活码体系概述

### 激活码类型
| 类型 | 有效期 | 说明 | 使用场景 |
|------|--------|------|----------|
| premium_1m | 30天 | 1个月高级会员 | 新用户体验、活动奖励 |
| premium_3m | 90天 | 3个月高级会员 | 季度促销、推广活动 |
| premium_6m | 180天 | 6个月高级会员 | 半年套餐、忠实用户奖励 |
| premium_1y | 365天 | 1年高级会员 | 年度套餐、重要合作伙伴 |

### 激活码格式
- **长度**: 16位字符
- **格式**: XXXX-XXXX-XXXX-XXXX
- **字符集**: 大写字母 + 数字 (去除易混淆字符 0,O,1,I,L)
- **示例**: ER9K-M3N7-Q8WX-F2H5

## 核心功能设计

### 1. 激活码生成

```java
@Service
public class ActivationCodeService {
    
    private static final String CHAR_SET = "23456789ABCDEFGHJKMNPQRSTUVWXYZ";
    private static final int CODE_LENGTH = 16;
    
    /**
     * 批量生成激活码
     */
    public List<ActivationCode> generateCodes(GenerateCodeRequest request) {
        String batchId = generateBatchId();
        List<ActivationCode> codes = new ArrayList<>();
        
        for (int i = 0; i < request.getQuantity(); i++) {
            ActivationCode code = new ActivationCode();
            code.setCode(generateUniqueCode());
            code.setCodeType(request.getCodeType());
            code.setDurationDays(getDurationByType(request.getCodeType()));
            code.setBatchId(batchId);
            code.setCreatedBy(request.getAdminId());
            code.setExpireTime(calculateExpireTime(request.getValidDays()));
            code.setRemark(request.getRemark());
            code.setStatus(0); // 未使用
            
            codes.add(code);
        }
        
        // 批量插入数据库
        activationCodeMapper.insertBatch(codes);
        
        return codes;
    }
    
    /**
     * 生成唯一激活码
     */
    private String generateUniqueCode() {
        String code;
        do {
            code = generateRandomCode();
        } while (activationCodeMapper.existsByCode(code));
        
        return code;
    }
    
    /**
     * 生成随机码
     */
    private String generateRandomCode() {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            if (i > 0 && i % 4 == 0) {
                sb.append("-");
            }
            sb.append(CHAR_SET.charAt(random.nextInt(CHAR_SET.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 根据类型获取天数
     */
    private int getDurationByType(String codeType) {
        switch (codeType) {
            case "premium_1m": return 30;
            case "premium_3m": return 90;
            case "premium_6m": return 180;
            case "premium_1y": return 365;
            default: return 30;
        }
    }
}
```

### 2. 激活码使用

```java
/**
 * 使用激活码
 */
public Result<String> useActivationCode(String userId, String code) {
    // 1. 验证激活码
    ActivationCode activationCode = activationCodeMapper.selectByCode(code);
    if (activationCode == null) {
        return Result.error("激活码不存在");
    }
    
    if (activationCode.getStatus() == 1) {
        return Result.error("激活码已被使用");
    }
    
    if (activationCode.getStatus() == 2 || 
        activationCode.getExpireTime().before(new Date())) {
        return Result.error("激活码已过期");
    }
    
    // 2. 获取用户信息
    User user = userService.getByUserId(userId);
    if (user == null) {
        return Result.error("用户不存在");
    }
    
    // 3. 计算新的会员到期时间
    Date newExpireTime = calculateNewExpireTime(user, activationCode);
    
    // 4. 更新用户信息
    user.setRole("premium");
    user.setDailyQuota(150);
    user.setPremiumExpire(newExpireTime);
    userService.updateById(user);
    
    // 5. 标记激活码为已使用
    activationCode.setStatus(1);
    activationCode.setUsedBy(userId);
    activationCode.setUsedTime(new Date());
    activationCodeMapper.updateById(activationCode);
    
    // 6. 记录使用日志
    logActivationCodeUsage(userId, code, activationCode.getCodeType());
    
    return Result.success("激活成功，会员有效期至：" + 
        DateUtil.format(newExpireTime, "yyyy-MM-dd"));
}

/**
 * 计算新的到期时间
 */
private Date calculateNewExpireTime(User user, ActivationCode code) {
    Date baseDate = new Date();
    
    // 如果用户当前是高级会员且未过期，则在原有基础上延长
    if ("premium".equals(user.getRole()) && 
        user.getPremiumExpire() != null && 
        user.getPremiumExpire().after(baseDate)) {
        baseDate = user.getPremiumExpire();
    }
    
    // 添加激活码对应的天数
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(baseDate);
    calendar.add(Calendar.DAY_OF_MONTH, code.getDurationDays());
    
    return calendar.getTime();
}
```

### 3. 管理员功能

```java
@RestController
@RequestMapping("/api/admin/activation-codes")
public class ActivationCodeController {
    
    /**
     * 生成激活码
     */
    @PostMapping("/generate")
    public Result<List<ActivationCode>> generateCodes(@RequestBody GenerateCodeRequest request) {
        // 验证管理员权限
        if (!isAdmin(request.getAdminId())) {
            return Result.error("权限不足");
        }
        
        List<ActivationCode> codes = activationCodeService.generateCodes(request);
        return Result.success(codes);
    }
    
    /**
     * 查询激活码列表
     */
    @GetMapping("/list")
    public Result<PageResult<ActivationCode>> getCodeList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String batchId,
            @RequestParam(required = false) Integer status) {
        
        PageResult<ActivationCode> result = activationCodeService
            .getCodeList(page, size, batchId, status);
        
        return Result.success(result);
    }
    
    /**
     * 导出激活码
     */
    @GetMapping("/export/{batchId}")
    public void exportCodes(@PathVariable String batchId, HttpServletResponse response) {
        List<ActivationCode> codes = activationCodeService.getCodesByBatchId(batchId);
        
        // 生成Excel文件
        ExcelUtil.exportActivationCodes(codes, response);
    }
    
    /**
     * 批量作废激活码
     */
    @PostMapping("/revoke")
    public Result<String> revokeCodes(@RequestBody List<String> codes) {
        int count = activationCodeService.revokeCodes(codes);
        return Result.success("成功作废 " + count + " 个激活码");
    }
}
```

## 数据统计和监控

### 1. 激活码使用统计

```java
/**
 * 激活码统计信息
 */
@GetMapping("/statistics")
public Result<ActivationCodeStatistics> getStatistics(
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
    
    ActivationCodeStatistics stats = new ActivationCodeStatistics();
    
    // 总生成数量
    stats.setTotalGenerated(activationCodeMapper.countByDateRange(startDate, endDate));
    
    // 已使用数量
    stats.setTotalUsed(activationCodeMapper.countUsedByDateRange(startDate, endDate));
    
    // 已过期数量
    stats.setTotalExpired(activationCodeMapper.countExpiredByDateRange(startDate, endDate));
    
    // 使用率
    stats.setUsageRate(stats.getTotalUsed() * 100.0 / stats.getTotalGenerated());
    
    // 按类型统计
    stats.setTypeStatistics(activationCodeMapper.getTypeStatistics(startDate, endDate));
    
    return Result.success(stats);
}
```

### 2. 定时任务

```java
@Component
public class ActivationCodeScheduler {
    
    /**
     * 每日检查过期激活码
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void checkExpiredCodes() {
        int count = activationCodeService.markExpiredCodes();
        log.info("标记过期激活码数量: {}", count);
    }
    
    /**
     * 清理过期激活码数据
     */
    @Scheduled(cron = "0 0 3 1 * ?") // 每月1号凌晨3点执行
    public void cleanExpiredCodes() {
        // 清理6个月前的过期激活码
        Date cutoffDate = DateUtil.offsetMonth(new Date(), -6);
        int count = activationCodeService.deleteExpiredCodes(cutoffDate);
        log.info("清理过期激活码数量: {}", count);
    }
}
```

## 安全措施

### 1. 防刷机制
- **IP限制**: 同一IP每小时最多使用5个激活码
- **用户限制**: 同一用户每天最多使用3个激活码
- **设备限制**: 同一设备每天最多使用2个激活码

### 2. 激活码保护
- **加密存储**: 激活码在数据库中加密存储
- **访问日志**: 记录所有激活码相关操作
- **权限控制**: 只有管理员可以生成和查看激活码

### 3. 异常监控
- **批量使用告警**: 短时间内大量激活码被使用
- **异常IP告警**: 来自异常IP的激活码使用
- **过期率告警**: 激活码过期率过高

## 业务规则

### 1. 激活码生成规则
- 管理员可以设置激活码的有效期
- 支持批量生成，单次最多1000个
- 每个批次都有唯一的批次ID
- 激活码生成后不可修改

### 2. 激活码使用规则
- 每个激活码只能使用一次
- 激活码有使用期限，过期自动失效
- 会员时间可以叠加，不会覆盖
- 使用后立即生效

### 3. 会员权益规则
- 激活码激活的会员享受完整权益
- 会员到期后自动降级为免费用户
- 支持会员时间延长，不支持缩短

## 接口设计

### 用户端接口
```
POST /api/user/activation-code/use
参数: { "code": "ER9K-M3N7-Q8WX-F2H5" }
返回: { "success": true, "message": "激活成功", "expireTime": "2024-12-31" }
```

### 管理端接口
```
POST /api/admin/activation-codes/generate
参数: { "codeType": "premium_1m", "quantity": 100, "validDays": 30, "remark": "新用户活动" }

GET /api/admin/activation-codes/list
参数: page, size, batchId, status

GET /api/admin/activation-codes/statistics
参数: startDate, endDate
```
