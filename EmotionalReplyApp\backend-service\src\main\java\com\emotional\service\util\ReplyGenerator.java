package com.emotional.service.util;

import com.emotional.service.dto.EmotionAnalyzeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 回复生成工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Component
public class ReplyGenerator {
    
    // 回复模板映射
    private static final Map<String, Map<String, List<String>>> REPLY_TEMPLATES = new HashMap<>();
    
    // 风格名称映射
    private static final Map<String, String> STYLE_NAMES = new HashMap<>();
    
    static {
        initializeStyleNames();
        initializeReplyTemplates();
    }
    
    /**
     * 初始化风格名称
     */
    private static void initializeStyleNames() {
        STYLE_NAMES.put("warm_caring", "温暖关怀");
        STYLE_NAMES.put("humorous", "幽默风趣");
        STYLE_NAMES.put("rational", "理性分析");
        STYLE_NAMES.put("concise", "简洁直接");
        STYLE_NAMES.put("romantic", "浪漫情话");
        STYLE_NAMES.put("encouraging", "鼓励支持");
        STYLE_NAMES.put("professional", "专业正式");
        STYLE_NAMES.put("casual", "轻松随意");
    }
    
    /**
     * 初始化回复模板
     */
    private static void initializeReplyTemplates() {
        // 开心情感的回复模板
        Map<String, List<String>> happyTemplates = new HashMap<>();
        happyTemplates.put("warm_caring", Arrays.asList(
            "看到你这么开心，我也很高兴！继续保持这份好心情吧！",
            "你的快乐真的很有感染力，希望你每天都能这么开心！",
            "真为你感到高兴，愿你的笑容永远灿烂！"
        ));
        happyTemplates.put("humorous", Arrays.asList(
            "哈哈，你的快乐都要溢出屏幕了！🎉 感染力十足啊！",
            "看来今天是个好日子，连空气都变甜了！😄",
            "你这么开心，是不是中彩票了？快分享一下秘诀！"
        ));
        happyTemplates.put("rational", Arrays.asList(
            "很高兴看到你心情不错，这种积极的状态很棒，对身心健康都有好处。",
            "保持积极乐观的心态确实很重要，这会让生活更美好。",
            "正面情绪有助于提高工作效率和生活质量，继续保持！"
        ));
        happyTemplates.put("concise", Arrays.asList(
            "真为你高兴！",
            "太棒了！",
            "开心就好！"
        ));
        happyTemplates.put("romantic", Arrays.asList(
            "你的笑容是我见过最美的风景，愿你永远这么开心。💕",
            "看到你开心，我的心也跟着飞起来了。❤️",
            "你的快乐就是我最大的幸福。😘"
        ));
        REPLY_TEMPLATES.put("开心", happyTemplates);
        
        // 难过情感的回复模板
        Map<String, List<String>> sadTemplates = new HashMap<>();
        sadTemplates.put("warm_caring", Arrays.asList(
            "听起来你遇到了一些困难，我很关心你。如果需要帮助或者想聊聊，我随时都在。",
            "我能感受到你的难过，虽然我不能替你承受痛苦，但我会陪伴你度过这段时光。",
            "每个人都会有低谷期，这很正常。相信你有足够的勇气和智慧走出来。"
        ));
        sadTemplates.put("humorous", Arrays.asList(
            "虽然现在有点难过，但记住，彩虹总在风雨后！🌈 而且我会陪着你等彩虹！",
            "难过的时候就想想，至少你还有我这个贴心的朋友！😊",
            "生活就像过山车，有低谷就有高峰，现在只是在谷底准备起飞！"
        ));
        sadTemplates.put("rational", Arrays.asList(
            "我理解你现在的感受。遇到问题时，我们可以一步步分析解决方案。你觉得最主要的问题是什么？",
            "情绪低落是人之常情，重要的是如何调节和应对。你可以尝试一些放松的活动。",
            "困难是暂时的，关键是要保持理性思考，寻找解决问题的方法。"
        ));
        sadTemplates.put("concise", Arrays.asList(
            "抱抱，会好起来的。",
            "别难过，我在这里。",
            "一切都会过去的。"
        ));
        sadTemplates.put("romantic", Arrays.asList(
            "亲爱的，无论发生什么，我都会陪在你身边。你的快乐就是我的快乐。💕",
            "我的肩膀永远为你准备着，想哭就哭吧，我会默默陪伴。❤️",
            "你是我心中最珍贵的宝贝，我会用我的爱来治愈你的伤痛。"
        ));
        REPLY_TEMPLATES.put("难过", sadTemplates);
        
        // 愤怒情感的回复模板
        Map<String, List<String>> angryTemplates = new HashMap<>();
        angryTemplates.put("warm_caring", Arrays.asList(
            "我能感受到你的愤怒，先深呼吸一下，我们慢慢聊。",
            "生气是正常的，但别让愤怒伤害了自己。我们一起想想解决办法。",
            "我理解你的感受，愤怒背后往往是受伤或失望。"
        ));
        angryTemplates.put("humorous", Arrays.asList(
            "哇，感觉你现在像个小火龙！🔥 要不要我帮你灭灭火？",
            "生气的时候数到十，如果还生气就数到一百！😄",
            "愤怒是魔鬼，但你比魔鬼更强大！"
        ));
        angryTemplates.put("rational", Arrays.asList(
            "愤怒是正常的情绪反应，让我们冷静分析一下是什么让你这么生气。",
            "冲动是魔鬼，理性思考才能找到最好的解决方案。",
            "愤怒往往会影响判断力，建议先冷静下来再做决定。"
        ));
        angryTemplates.put("concise", Arrays.asList(
            "先冷静一下。",
            "深呼吸。",
            "别生气了。"
        ));
        angryTemplates.put("romantic", Arrays.asList(
            "我知道你很生气，但在我心里你永远是最可爱的。💕",
            "即使你生气的样子也很迷人，但我更喜欢你笑的时候。",
            "让我的爱来平息你心中的怒火吧。❤️"
        ));
        REPLY_TEMPLATES.put("愤怒", angryTemplates);
        
        // 添加更多情感模板...
        initializeOtherEmotionTemplates();
    }
    
    /**
     * 初始化其他情感模板
     */
    private static void initializeOtherEmotionTemplates() {
        // 担心情感
        Map<String, List<String>> worriedTemplates = new HashMap<>();
        worriedTemplates.put("warm_caring", Arrays.asList(
            "我能理解你的担心，有什么具体的事情让你焦虑吗？我们一起想办法。",
            "担心说明你很在乎，这是好事。但过度担心会影响健康，试着放松一下。"
        ));
        worriedTemplates.put("humorous", Arrays.asList(
            "担心虫又来了吗？🐛 别怕，我来帮你赶走它们！",
            "担心就像摇椅，让你有事可做，但哪儿也去不了！"
        ));
        worriedTemplates.put("rational", Arrays.asList(
            "担心往往来自于对未知的恐惧，我们可以列出具体的担忧点，逐一分析解决。",
            "大部分担心的事情都不会发生，专注于当下更重要。"
        ));
        worriedTemplates.put("concise", Arrays.asList(
            "别担心，有我在。",
            "一切都会好的。"
        ));
        worriedTemplates.put("romantic", Arrays.asList(
            "不要害怕，我会保护你，陪你度过每一个难关。💕",
            "有我在身边，你还担心什么呢？❤️"
        ));
        REPLY_TEMPLATES.put("担心", worriedTemplates);
        
        // 平静情感
        Map<String, List<String>> calmTemplates = new HashMap<>();
        calmTemplates.put("warm_caring", Arrays.asList(
            "你看起来很平静，这种状态很好。有什么想聊的吗？",
            "平静是一种智慧，保持这种心境很不容易。"
        ));
        calmTemplates.put("humorous", Arrays.asList(
            "平静如水的你，是不是在思考人生的大问题？🤔",
            "这么淡定，是修炼成仙了吗？"
        ));
        calmTemplates.put("rational", Arrays.asList(
            "平静是一种很好的心理状态，有利于理性思考。",
            "保持内心平静在这个浮躁的世界里很难得。"
        ));
        calmTemplates.put("concise", Arrays.asList(
            "嗯，了解。",
            "好的。"
        ));
        calmTemplates.put("romantic", Arrays.asList(
            "你的平静让我感到安心，就像港湾一样温暖。💕",
            "和你在一起，我的心也变得平静了。"
        ));
        REPLY_TEMPLATES.put("平静", calmTemplates);
        
        // 关心情感
        Map<String, List<String>> caringTemplates = new HashMap<>();
        caringTemplates.put("warm_caring", Arrays.asList(
            "感受到了你的关心，真的很温暖。谢谢你这么体贴。",
            "你的关心是我收到的最好的礼物，真的很感动。"
        ));
        caringTemplates.put("humorous", Arrays.asList(
            "哇，被关心的感觉真好！感觉自己像个小公主/小王子！👑",
            "你的关心比蜂蜜还甜呢！🍯"
        ));
        caringTemplates.put("rational", Arrays.asList(
            "你的关心很珍贵，这种互相关爱的关系很美好。",
            "感谢你的关心，这让我感受到了人与人之间的温暖。"
        ));
        caringTemplates.put("concise", Arrays.asList(
            "谢谢关心。",
            "很感动。"
        ));
        caringTemplates.put("romantic", Arrays.asList(
            "你的关心是我最珍贵的礼物，我也同样关心着你。💕",
            "被你关心是我最大的幸福。❤️"
        ));
        REPLY_TEMPLATES.put("关心", caringTemplates);
    }
    
    /**
     * 生成回复选项
     * 
     * @param message 原始消息
     * @param emotionResult 情感分析结果
     * @param replyStyles 指定的回复风格
     * @return 回复选项列表
     */
    public List<EmotionAnalyzeResponse.ReplyOption> generateReplies(String message,
                                                                   EmotionAnalyzeResponse.EmotionResult emotionResult,
                                                                   List<String> replyStyles) {
        List<EmotionAnalyzeResponse.ReplyOption> replyOptions = new ArrayList<>();
        
        String emotion = emotionResult.getEmotion();
        Map<String, List<String>> emotionTemplates = REPLY_TEMPLATES.get(emotion);
        
        // 如果没有找到对应情感的模板，使用平静情感的模板
        if (emotionTemplates == null) {
            emotionTemplates = REPLY_TEMPLATES.get("平静");
        }
        
        for (String style : replyStyles) {
            List<String> templates = emotionTemplates.get(style);
            if (templates != null && !templates.isEmpty()) {
                // 随机选择一个模板
                String template = templates.get(new Random().nextInt(templates.size()));
                
                EmotionAnalyzeResponse.ReplyOption option = new EmotionAnalyzeResponse.ReplyOption();
                option.setContent(template);
                option.setStyle(style);
                option.setStyleName(STYLE_NAMES.get(style));
                option.setRecommendation(calculateRecommendation(style, emotion));
                option.setConfidence(calculateReplyConfidence(emotionResult.getConfidence()));
                
                replyOptions.add(option);
            }
        }
        
        // 按推荐度排序
        replyOptions.sort((a, b) -> b.getRecommendation().compareTo(a.getRecommendation()));
        
        log.info("生成了 {} 个回复选项，情感: {}", replyOptions.size(), emotion);
        
        return replyOptions;
    }
    
    /**
     * 计算推荐度
     */
    private Integer calculateRecommendation(String style, String emotion) {
        // 根据情感和风格的匹配度计算推荐度
        Map<String, Integer> styleScores = new HashMap<>();
        
        switch (emotion) {
            case "开心":
                styleScores.put("warm_caring", 4);
                styleScores.put("humorous", 5);
                styleScores.put("romantic", 4);
                styleScores.put("concise", 3);
                styleScores.put("rational", 2);
                break;
            case "难过":
                styleScores.put("warm_caring", 5);
                styleScores.put("romantic", 4);
                styleScores.put("rational", 3);
                styleScores.put("humorous", 2);
                styleScores.put("concise", 3);
                break;
            case "愤怒":
                styleScores.put("rational", 5);
                styleScores.put("warm_caring", 4);
                styleScores.put("concise", 3);
                styleScores.put("humorous", 2);
                styleScores.put("romantic", 2);
                break;
            default:
                styleScores.put("warm_caring", 4);
                styleScores.put("rational", 4);
                styleScores.put("humorous", 3);
                styleScores.put("concise", 3);
                styleScores.put("romantic", 3);
        }
        
        return styleScores.getOrDefault(style, 3);
    }
    
    /**
     * 计算回复置信度
     */
    private Double calculateReplyConfidence(Double emotionConfidence) {
        // 基于情感分析置信度计算回复置信度
        return Math.min(95.0, emotionConfidence * 0.9 + 10);
    }
}
