package com.emotional.service.controller;

import com.emotional.service.common.Result;
import com.emotional.service.dto.request.GenerateCodeRequest;
import com.emotional.service.entity.ActivationCode;
import com.emotional.service.service.ActivationCodeService;
import com.emotional.service.utils.AdminUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 激活码控制器
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/activation-code")
@RequiredArgsConstructor
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
public class ActivationCodeController {
    
    private final ActivationCodeService activationCodeService;
    
    /**
     * 生成激活码（管理员功能）
     */
    @PostMapping("/generate")
    public Result<ActivationCode> generateActivationCode(@RequestBody @Valid GenerateCodeRequest request) {

        try {
            // TODO: 验证管理员权限
            // if (!AdminUtils.isAdmin(request.getAdminId())) {
            //     return Result.error("权限不足，只有管理员可以生成激活码");
            // }

            ActivationCode activationCode = activationCodeService.generateActivationCode(
                    request.getCodeType(),
                    request.getDurationDays(),
                    request.getAdminId(),
                    request.getRemark());

            return Result.success("激活码生成成功", activationCode);

        } catch (Exception e) {
            log.error("生成激活码失败", e);
            return Result.error("生成激活码失败: " + e.getMessage());
        }
    }
    
    /**
     * 使用激活码
     */
    @PostMapping("/use")
    public Result<String> useActivationCode(
            @RequestParam @NotBlank String code,
            @RequestParam @NotNull Long userId) {
        
        try {
            boolean success = activationCodeService.useActivationCode(code, userId);
            
            if (success) {
                return Result.success("激活码使用成功，VIP权限已激活");
            } else {
                return Result.error("激活码使用失败，请检查激活码是否正确或已过期");
            }
            
        } catch (Exception e) {
            log.error("使用激活码失败: code={}, userId={}", code, userId, e);
            return Result.error("使用激活码失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证激活码
     */
    @GetMapping("/validate")
    public Result<Boolean> validateActivationCode(@RequestParam @NotBlank String code) {

        try {
            boolean isValid = activationCodeService.isValidCode(code);

            if (isValid) {
                return Result.success("激活码有效", true);
            } else {
                return Result.success("激活码无效或已过期", false);
            }

        } catch (Exception e) {
            log.error("验证激活码失败: code={}", code, e);
            return Result.error("验证激活码失败: " + e.getMessage());
        }
    }

    /**
     * 获取激活码列表（管理员功能）
     */
    @GetMapping("/list")
    public Result<List<ActivationCode>> getActivationCodeList(
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String codeType) {

        try {
            // TODO: 验证管理员权限

            List<ActivationCode> activationCodes = activationCodeService.getActivationCodeList(status, codeType);

            return Result.success("获取激活码列表成功", activationCodes);

        } catch (Exception e) {
            log.error("获取激活码列表失败", e);
            return Result.error("获取激活码列表失败: " + e.getMessage());
        }
    }
}
