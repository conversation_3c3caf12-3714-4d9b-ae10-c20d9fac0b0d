<template>
  <view class="container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 应用标题区域 -->
      <view class="header">
        <view class="app-icon">
          <text class="icon-emoji">📝</text>
        </view>
        <text class="app-name">注册账号</text>
        <text class="app-desc">加入情感回复助手</text>
      </view>
      
      <!-- 注册表单卡片 -->
      <view class="form-section">
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">👤</text>
            <input 
              v-model="registerForm.username"
              class="input-field"
              placeholder="请输入用户名"
              :maxlength="20"
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">📧</text>
            <input
              v-model="registerForm.email"
              class="input-field"
              placeholder="请输入邮箱地址"
              type="email"
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input 
              v-model="registerForm.password"
              class="input-field"
              placeholder="请输入密码"
              :type="showPassword ? 'text' : 'password'"
              :maxlength="20"
            />
            <text class="password-toggle" @click="togglePassword">
              {{ showPassword ? '🙈' : '👁️' }}
            </text>
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔐</text>
            <input
              v-model="registerForm.confirmPassword"
              class="input-field"
              placeholder="请确认密码"
              :type="showConfirmPassword ? 'text' : 'password'"
              :maxlength="20"
            />
            <text class="password-toggle" @click="toggleConfirmPassword">
              {{ showConfirmPassword ? '🙈' : '👁️' }}
            </text>
          </view>
        </view>

        <!-- 密码要求提示 -->
        <view class="password-tips">
          <text class="tips-title">密码要求：</text>
          <text class="tips-item">• 长度6-20位</text>
          <text class="tips-item">• 必须包含数字</text>
          <text class="tips-item">• 可包含字母和特殊符号：!@#$%^&amp;*()_+-=[]{}|;:,.&lt;&gt;?</text>
        </view>
        
        <!-- 协议文本 -->
        <view class="agreement-section">
          <view class="agreement-checkbox" @click="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreeToTerms }">
              <text class="check-icon" v-if="agreeToTerms">✓</text>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @click.stop="showAgreement">《用户协议》</text>
              和
              <text class="link-text" @click.stop="showPrivacy">《隐私政策》</text>
            </text>
          </view>
        </view>
        
        <button 
          class="register-btn"
          :class="{ disabled: !canRegister, loading: loading }"
          @click="handleRegister"
        >
          <text class="btn-text">{{ loading ? '注册中...' : '立即注册' }}</text>
          <view class="loading-spinner" v-if="loading"></view>
        </button>
        
        <view class="login-section">
          <text class="login-text">已有账号？</text>
          <text class="login-link" @click="goToLogin">立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'

export default {
  name: 'RegisterPage',
  
  data() {
    return {
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: ''
      },
      showPassword: false,
      showConfirmPassword: false,
      agreeToTerms: false,
      loading: false
    }
  },
  
  computed: {
    canRegister() {
      return this.registerForm.username.trim() &&
             this.registerForm.email.trim() &&
             this.registerForm.password.trim() &&
             this.registerForm.confirmPassword.trim() &&
             this.agreeToTerms &&
             !this.loading
    }
  },
  
  methods: {
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    // 切换确认密码显示
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },
    
    // 切换协议同意状态
    toggleAgreement() {
      this.agreeToTerms = !this.agreeToTerms
    },
    
    // 处理注册
    async handleRegister() {
      if (!this.canRegister) return
      
      // 验证密码强度
      if (this.registerForm.password.length < 6 || this.registerForm.password.length > 20) {
        uni.showToast({
          title: '密码长度必须为6-20位',
          icon: 'none'
        })
        return
      }

      // 密码验证：必须包含数字，可以包含字母和特殊符号
      const hasNumber = /\d/.test(this.registerForm.password)
      const validChars = /^[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;:,.<>?]*$/.test(this.registerForm.password)

      if (!hasNumber) {
        uni.showToast({
          title: '密码必须包含数字',
          icon: 'none'
        })
        return
      }

      if (!validChars) {
        uni.showToast({
          title: '密码包含不支持的字符',
          icon: 'none'
        })
        return
      }

      // 验证密码一致性
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        uni.showToast({
          title: '两次密码输入不一致',
          icon: 'none'
        })
        return
      }
      
      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(this.registerForm.email)) {
        uni.showToast({
          title: '请输入正确的邮箱地址',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      
      try {
        // 模拟注册API调用
        await this.delay(2000)
        
        // 注册成功，创建用户信息
        const userInfo = {
          id: Date.now(),
          username: this.registerForm.username,
          nickname: this.registerForm.username,
          email: this.registerForm.email,
          phone: '',
          avatar: '/static/images/default-avatar.png',
          isVip: false,
          dailyQuota: 10,
          todayUsed: 0,
          totalUsed: 0
        }
        
        UserManager.setUserInfo(userInfo)
        UserManager.setToken('register_token_' + Date.now())
        
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          AuthGuard.loginSuccessRedirect()
        }, 1500)
        
      } catch (error) {
        uni.showToast({
          title: '注册失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 去登录
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },
    
    // 显示用户协议
    showAgreement() {
      uni.showModal({
        title: '用户协议',
        content: '用户协议内容...',
        showCancel: false
      })
    },
    
    // 显示隐私政策
    showPrivacy() {
      uni.showModal({
        title: '隐私政策',
        content: '隐私政策内容...',
        showCancel: false
      })
    },
    
    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 主要内容区域
.main-content {
  flex: 1;
  padding: calc(80rpx + var(--status-bar-height, 0)) 30rpx 40rpx;
  display: flex;
  flex-direction: column;
}

// 应用标题区域
.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .app-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 30rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
    
    .icon-emoji {
      font-size: 60rpx;
    }
  }
  
  .app-name {
    display: block;
    color: white;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }
  
  .app-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
  }
}

// 注册表单卡片
.form-section {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  
  .input-group {
    margin-bottom: 30rpx;
    
    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      transition: all 0.3s ease;
      
      &:focus-within {
        border-color: #2196F3;
        background: white;
        box-shadow: 0 0 0 6rpx rgba(33, 150, 243, 0.1);
      }
      
      .input-icon {
        padding: 0 20rpx;
        font-size: 32rpx;
        color: #6c757d;
      }
      
      .input-field {
        flex: 1;
        height: 88rpx;
        padding: 0 20rpx;
        border: none;
        background: transparent;
        font-size: 28rpx;
        color: #333;
        
        &::placeholder {
          color: #adb5bd;
        }
      }
      
      .password-toggle {
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #6c757d;
        cursor: pointer;
      }
    }
  }

  // 密码要求提示
  .password-tips {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border-left: 4rpx solid #2196F3;

    .tips-title {
      display: block;
      font-size: 24rpx;
      color: #2196F3;
      font-weight: bold;
      margin-bottom: 8rpx;
    }

    .tips-item {
      display: block;
      font-size: 22rpx;
      color: #666;
      line-height: 1.4;
      margin-bottom: 4rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .agreement-section {
    margin-bottom: 30rpx;
    
    .agreement-checkbox {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      
      .checkbox {
        width: 36rpx;
        height: 36rpx;
        border: 2rpx solid #dee2e6;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
        margin-top: 2rpx;
        transition: all 0.3s ease;
        flex-shrink: 0;
        
        &.checked {
          background: #2196F3;
          border-color: #2196F3;
          
          .check-icon {
            color: white;
            font-size: 20rpx;
            font-weight: bold;
          }
        }
      }
      
      .agreement-text {
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
        
        .link-text {
          color: #2196F3;
          cursor: pointer;
        }
      }
    }
  }
  
  .register-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 16rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    
    .btn-text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid rgba(255, 255, 255, 0.3);
      border-top: 3rpx solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 16rpx;
    }
    
    &.disabled {
      background: #dee2e6;
      
      .btn-text {
        color: #6c757d;
      }
    }
    
    &.loading {
      pointer-events: none;
    }
    
    &:active {
      transform: translateY(2rpx);
    }
  }
  
  .login-section {
    text-align: center;
    
    .login-text {
      font-size: 26rpx;
      color: #666;
    }
    
    .login-link {
      color: #2196F3;
      margin-left: 8rpx;
      font-size: 26rpx;
      cursor: pointer;
    }
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
