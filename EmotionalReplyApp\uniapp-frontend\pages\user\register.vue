<template>
  <view class="container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 应用标题区域 -->
      <view class="header">
        <view class="app-icon">
          <text class="icon-emoji">📝</text>
        </view>
        <text class="app-name">注册账号</text>
        <text class="app-desc">加入情感回复助手</text>
      </view>
      
      <!-- 注册表单卡片 -->
      <view class="form-section">
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">👤</text>
            <input 
              v-model="registerForm.username"
              class="input-field"
              placeholder="请输入用户名"
              :maxlength="20"
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">📧</text>
            <input
              v-model="emailUsername"
              class="input-field"
              placeholder="请输入邮箱用户名"
              type="text"
              @input="onEmailUsernameInput"
              @focus="onEmailFocus"
              @blur="onEmailBlur"
            />
            <view class="email-suffix-container">
              <text class="email-suffix" @click="toggleEmailDropdown">
                {{ selectedEmailSuffix }}
              </text>
              <text class="dropdown-arrow" @click="toggleEmailDropdown">
                {{ showEmailDropdown ? '▲' : '▼' }}
              </text>
            </view>
          </view>

          <!-- 邮箱后缀下拉选择 -->
          <view class="email-dropdown" v-if="showEmailDropdown">
            <view
              class="dropdown-item"
              v-for="(suffix, index) in emailSuffixes"
              :key="index"
              @click="selectEmailSuffix(suffix)"
              :class="{ 'selected': suffix === selectedEmailSuffix }"
            >
              <text class="suggestion-text">{{ suffix }}</text>
              <text class="check-icon" v-if="suffix === selectedEmailSuffix">✓</text>
            </view>
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔢</text>
            <input
              v-model="registerForm.verificationCode"
              class="input-field"
              placeholder="请输入邮箱验证码"
              type="number"
              :maxlength="6"
            />
            <button
              class="code-btn"
              :class="{ disabled: !canSendCode || countdown > 0 }"
              @click="sendVerificationCode"
            >
              <text class="code-btn-text">
                {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
              </text>
            </button>
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔒</text>
            <input
              v-model="registerForm.password"
              class="input-field"
              placeholder="请输入密码"
              type="password"
              :maxlength="20"
            />
          </view>
        </view>
        
        <view class="input-group">
          <view class="input-wrapper">
            <text class="input-icon">🔐</text>
            <input
              v-model="registerForm.confirmPassword"
              class="input-field"
              placeholder="请确认密码"
              type="password"
              :maxlength="20"
            />
          </view>
        </view>


        
        <!-- 协议文本 -->
        <view class="agreement-section">
          <view class="agreement-checkbox" @click="toggleAgreement">
            <view class="checkbox" :class="{ checked: agreeToTerms }">
              <text class="check-icon" v-if="agreeToTerms">✓</text>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @click.stop="showAgreement">《用户协议》</text>
              和
              <text class="link-text" @click.stop="showPrivacy">《隐私政策》</text>
            </text>
          </view>
        </view>

        <!-- 调试按钮 -->
        <button
          class="debug-btn"
          @click="debugConnection"
          style="width: 100%; height: 60rpx; background: #ff9800; border: none; border-radius: 12rpx; margin-bottom: 20rpx; display: flex; align-items: center; justify-content: center;"
        >
          <text style="color: white; font-size: 24rpx;">🔧 调试连接</text>
        </button>

        <button
          class="register-btn"
          :class="{ disabled: !canRegister, loading: loading }"
          @click="handleRegister"
        >
          <text class="btn-text">{{ loading ? '注册中...' : '立即注册' }}</text>
          <view class="loading-spinner" v-if="loading"></view>
        </button>
        
        <view class="login-section">
          <text class="login-text">已有账号？</text>
          <text class="login-link" @click="goToLogin">立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'
import { sendRegisterCode, verifyRegisterCode } from '../../api/user.js'

export default {
  name: 'RegisterPage',
  
  data() {
    return {
      registerForm: {
        username: '',
        email: '',
        verificationCode: '',
        password: '',
        confirmPassword: ''
      },
      agreeToTerms: false,
      loading: false,
      countdown: 0,
      countdownTimer: null,
      emailUsername: '',
      selectedEmailSuffix: '@qq.com',
      showEmailDropdown: false,
      emailSuffixes: [
        '@qq.com',
        '@163.com',
        '@126.com',
        '@gmail.com'
      ]
    }
  },
  
  computed: {
    canRegister() {
      return this.registerForm.username.trim() &&
             this.registerForm.email.trim() &&
             this.registerForm.verificationCode.trim() &&
             this.registerForm.password.trim() &&
             this.registerForm.confirmPassword.trim() &&
             this.agreeToTerms &&
             !this.loading
    },

    canSendCode() {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return this.registerForm.email.trim() && emailRegex.test(this.registerForm.email)
    }
  },

  watch: {
    // 监听邮箱用户名和后缀变化，自动更新完整邮箱
    emailUsername: {
      handler(newVal) {
        this.updateFullEmail()
      },
      immediate: true
    },
    selectedEmailSuffix: {
      handler(newVal) {
        this.updateFullEmail()
      },
      immediate: true
    }
  },

  onUnload() {
    // 清理定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
      this.countdownTimer = null
    }
  },

  methods: {
    // 更新完整邮箱地址
    updateFullEmail() {
      if (this.emailUsername.trim()) {
        this.registerForm.email = this.emailUsername + this.selectedEmailSuffix
      } else {
        this.registerForm.email = ''
      }
    },

    // 邮箱用户名输入处理
    onEmailUsernameInput(e) {
      this.emailUsername = e.detail.value
    },

    // 邮箱输入框获得焦点
    onEmailFocus() {
      // 焦点时不自动显示下拉框，只有点击后缀时才显示
    },

    // 邮箱输入框失去焦点
    onEmailBlur() {
      // 延迟隐藏，让点击事件能够触发
      setTimeout(() => {
        this.showEmailDropdown = false
      }, 200)
    },

    // 切换邮箱下拉框
    toggleEmailDropdown() {
      this.showEmailDropdown = !this.showEmailDropdown
    },

    // 选择邮箱后缀
    selectEmailSuffix(suffix) {
      this.selectedEmailSuffix = suffix
      this.showEmailDropdown = false
    },

    // 测试服务器连接
    async testConnection() {
      try {
        console.log('测试服务器连接...')
        const response = await uni.request({
          url: 'http://localhost:5000/api/health',
          method: 'GET',
          timeout: 5000
        })
        console.log('服务器连接测试结果:', response)
        return response.statusCode === 200
      } catch (error) {
        console.error('服务器连接测试失败:', error)
        return false
      }
    },

    // 发送邮箱验证码
    async sendVerificationCode() {
      if (!this.canSendCode || this.countdown > 0) return

      let loadingShown = false

      try {
        console.log('开始发送验证码，邮箱:', this.registerForm.email)

        uni.showLoading({
          title: '发送中...'
        })
        loadingShown = true

        // 检查网络连接
        const networkType = await uni.getNetworkType()
        console.log('网络状态:', networkType)

        if (networkType.networkType === 'none') {
          throw new Error('网络连接不可用')
        }

        // 测试服务器连接
        const serverConnected = await this.testConnection()
        if (!serverConnected) {
          throw new Error('无法连接到服务器，请确保后端服务已启动')
        }

        // 调用后端发送注册验证码API
        console.log('调用API: /email/send-register-email')
        const result = await sendRegisterCode(this.registerForm.email)
        console.log('API响应:', result)

        if (result && result.success !== false) {
          uni.showToast({
            title: '验证码已发送至邮箱',
            icon: 'success'
          })

          // 开始倒计时
          this.startCountdown()
        } else {
          uni.showToast({
            title: result?.message || '发送失败',
            icon: 'none'
          })
        }

      } catch (error) {
        console.error('发送验证码失败:', error)

        let errorMessage = '发送失败，请重试'

        // 根据错误类型提供更具体的提示
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接'
          } else if (error.errMsg.includes('fail')) {
            errorMessage = '网络连接失败，请检查网络设置'
          } else if (error.errMsg.includes('abort')) {
            errorMessage = '请求被中断，请重试'
          }
        } else if (error.message) {
          errorMessage = error.message
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      } finally {
        // 确保loading被隐藏
        if (loadingShown) {
          uni.hideLoading()
        }
      }
    },

    // 开始倒计时
    startCountdown() {
      this.countdown = 60
      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    },

    // 切换协议同意状态
    toggleAgreement() {
      this.agreeToTerms = !this.agreeToTerms
    },
    
    // 处理注册
    async handleRegister() {
      if (!this.canRegister) return
      
      // 验证密码强度
      if (this.registerForm.password.length < 6 || this.registerForm.password.length > 20) {
        uni.showToast({
          title: '密码长度必须为6-20位',
          icon: 'none'
        })
        return
      }

      // 密码验证：必须包含数字，可以包含字母和特殊符号
      const hasNumber = /\d/.test(this.registerForm.password)
      const validChars = /^[A-Za-z\d!@#$%^&*()_+\-=\[\]{}|;:,.<>?]*$/.test(this.registerForm.password)

      if (!hasNumber) {
        uni.showToast({
          title: '密码必须包含数字',
          icon: 'none'
        })
        return
      }

      if (!validChars) {
        uni.showToast({
          title: '密码包含不支持的字符',
          icon: 'none'
        })
        return
      }

      // 验证密码一致性
      if (this.registerForm.password !== this.registerForm.confirmPassword) {
        uni.showToast({
          title: '两次密码输入不一致',
          icon: 'none'
        })
        return
      }
      
      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(this.registerForm.email)) {
        uni.showToast({
          title: '请输入正确的邮箱地址',
          icon: 'none'
        })
        return
      }

      // 验证邮箱验证码
      if (!this.registerForm.verificationCode || this.registerForm.verificationCode.length !== 6) {
        uni.showToast({
          title: '请输入6位验证码',
          icon: 'none'
        })
        return
      }

      this.loading = true

      try {
        // 先验证邮箱验证码
        const verifyResult = await verifyRegisterCode(this.registerForm.email, this.registerForm.verificationCode)

        if (!verifyResult || verifyResult.success === false) {
          uni.showToast({
            title: verifyResult?.message || '验证码错误',
            icon: 'none'
          })
          this.loading = false
          return
        }

        // 模拟注册API调用
        await this.delay(1500)
        
        // 注册成功，创建用户信息
        const userInfo = {
          id: Date.now(),
          username: this.registerForm.username,
          nickname: this.registerForm.username,
          email: this.registerForm.email,
          phone: '',
          avatar: '/static/images/default-avatar.png',
          isVip: false,
          dailyQuota: 10,
          todayUsed: 0,
          totalUsed: 0
        }
        
        UserManager.setUserInfo(userInfo)
        UserManager.setToken('register_token_' + Date.now())
        
        uni.showToast({
          title: '注册成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          AuthGuard.loginSuccessRedirect()
        }, 1500)
        
      } catch (error) {
        uni.showToast({
          title: '注册失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 去登录
    goToLogin() {
      uni.navigateTo({
        url: '/pages/login/login'
      })
    },
    
    // 显示用户协议
    showAgreement() {
      uni.showModal({
        title: '用户协议',
        content: '用户协议内容...',
        showCancel: false
      })
    },
    
    // 显示隐私政策
    showPrivacy() {
      uni.showModal({
        title: '隐私政策',
        content: '隐私政策内容...',
        showCancel: false
      })
    },
    
    // 调试连接
    async debugConnection() {
      console.log('=== 开始调试连接 ===')

      try {
        // 1. 检查网络状态
        const networkType = await uni.getNetworkType()
        console.log('1. 网络状态:', networkType)

        // 2. 测试基础连接
        console.log('2. 测试基础连接...')
        const testResponse = await uni.request({
          url: 'http://localhost:5000',
          method: 'GET',
          timeout: 5000
        })
        console.log('基础连接测试结果:', testResponse)

        // 3. 测试API连接
        console.log('3. 测试API连接...')
        const apiResponse = await uni.request({
          url: 'http://localhost:5000/api/email/send-register-email',
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: {
            email: '<EMAIL>'
          },
          timeout: 10000
        })
        console.log('API连接测试结果:', apiResponse)

        uni.showModal({
          title: '调试结果',
          content: `网络: ${networkType.networkType}\n基础连接: ${testResponse.statusCode}\nAPI连接: ${apiResponse.statusCode}`,
          showCancel: false
        })

      } catch (error) {
        console.error('调试连接失败:', error)
        uni.showModal({
          title: '调试失败',
          content: `错误信息: ${error.errMsg || error.message || '未知错误'}`,
          showCancel: false
        })
      }
    },

    // 延迟函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

// 主要内容区域
.main-content {
  flex: 1;
  padding: calc(80rpx + var(--status-bar-height, 0)) 30rpx 40rpx;
  display: flex;
  flex-direction: column;
}

// 应用标题区域
.header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .app-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 30rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
    
    .icon-emoji {
      font-size: 60rpx;
    }
  }
  
  .app-name {
    display: block;
    color: white;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 16rpx;
  }
  
  .app-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 28rpx;
  }
}

// 注册表单卡片
.form-section {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  
  .input-group {
    margin-bottom: 30rpx;
    position: relative;
    
    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 16rpx;
      border: 2rpx solid #e9ecef;
      transition: all 0.3s ease;
      
      &:focus-within {
        border-color: #2196F3;
        background: white;
        box-shadow: 0 0 0 6rpx rgba(33, 150, 243, 0.1);
      }
      
      .input-icon {
        padding: 0 20rpx;
        font-size: 32rpx;
        color: #6c757d;
      }
      
      .input-field {
        flex: 1;
        height: 88rpx;
        padding: 0 20rpx;
        border: none;
        background: transparent;
        font-size: 28rpx;
        color: #333;

        &::placeholder {
          color: #adb5bd;
        }
      }

      .code-btn {
        padding: 12rpx 20rpx;
        background: #2196F3;
        border: none;
        border-radius: 12rpx;
        margin-left: 20rpx;
        margin-right: 10rpx;
        min-width: 140rpx;
        height: 56rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        flex-shrink: 0;

        .code-btn-text {
          color: white;
          font-size: 22rpx;
          font-weight: 500;
        }

        &.disabled {
          background: #dee2e6;

          .code-btn-text {
            color: #6c757d;
          }
        }

        &:active:not(.disabled) {
          transform: scale(0.95);
          background: #1976D2;
        }
      }
      
      .password-toggle {
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #6c757d;
        cursor: pointer;
      }

      .email-suffix-container {
        display: flex;
        align-items: center;
        border-left: 1rpx solid #e9ecef;
        padding: 0 10rpx;
        min-width: 160rpx;

        .email-suffix {
          font-size: 28rpx;
          color: #6c757d;
          cursor: pointer;
          padding: 8rpx 12rpx;
          border-radius: 8rpx;
          transition: all 0.3s ease;

          &:hover {
            background: #f8f9fa;
            color: #2196F3;
          }
        }

        .dropdown-arrow {
          font-size: 20rpx;
          color: #adb5bd;
          margin-left: 8rpx;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            color: #2196F3;
          }
        }
      }
    }

    .email-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1rpx solid #e9ecef;
      border-radius: 12rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      z-index: 1000;
      max-height: 400rpx;
      overflow-y: auto;

      .dropdown-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 24rpx;
        border-bottom: 1rpx solid #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f8f9fa;
        }

        &.selected {
          background: #e3f2fd;
          color: #2196F3;
        }

        .suggestion-text {
          font-size: 28rpx;
          color: #333;
        }

        .check-icon {
          font-size: 24rpx;
          color: #2196F3;
          font-weight: bold;
        }

        &.selected .suggestion-text {
          color: #2196F3;
          font-weight: 500;
        }
      }
    }
  }

  // 密码要求提示
  .password-tips {
    margin-bottom: 30rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    border-left: 4rpx solid #2196F3;

    .tips-title {
      display: block;
      font-size: 24rpx;
      color: #2196F3;
      font-weight: bold;
      margin-bottom: 8rpx;
    }

    .tips-item {
      display: block;
      font-size: 22rpx;
      color: #666;
      line-height: 1.4;
      margin-bottom: 4rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .agreement-section {
    margin-bottom: 30rpx;
    
    .agreement-checkbox {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      
      .checkbox {
        width: 36rpx;
        height: 36rpx;
        border: 2rpx solid #dee2e6;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12rpx;
        margin-top: 2rpx;
        transition: all 0.3s ease;
        flex-shrink: 0;
        
        &.checked {
          background: #2196F3;
          border-color: #2196F3;
          
          .check-icon {
            color: white;
            font-size: 20rpx;
            font-weight: bold;
          }
        }
      }
      
      .agreement-text {
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
        
        .link-text {
          color: #2196F3;
          cursor: pointer;
        }
      }
    }
  }
  
  .register-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 16rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    
    .btn-text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid rgba(255, 255, 255, 0.3);
      border-top: 3rpx solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-left: 16rpx;
    }
    
    &.disabled {
      background: #dee2e6;
      
      .btn-text {
        color: #6c757d;
      }
    }
    
    &.loading {
      pointer-events: none;
    }
    
    &:active {
      transform: translateY(2rpx);
    }
  }
  
  .login-section {
    text-align: center;
    
    .login-text {
      font-size: 26rpx;
      color: #666;
    }
    
    .login-link {
      color: #2196F3;
      margin-left: 8rpx;
      font-size: 26rpx;
      cursor: pointer;
    }
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
