package com.emotional.service.util;

import com.emotional.service.dto.EmotionAnalyzeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 情感分析工具类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Component
public class EmotionAnalyzer {
    
    // 情感关键词映射
    private static final Map<String, List<String>> EMOTION_KEYWORDS = new HashMap<>();
    
    // 情感强度词汇
    private static final Map<String, Integer> INTENSITY_WORDS = new HashMap<>();
    
    static {
        // 初始化情感关键词
        EMOTION_KEYWORDS.put("开心", Arrays.asList(
            "开心", "高兴", "快乐", "兴奋", "愉快", "欣喜", "喜悦", "满足", "幸福",
            "哈哈", "嘻嘻", "😊", "😄", "😃", "🎉", "👍", "棒", "太好了", "amazing"
        ));
        
        EMOTION_KEYWORDS.put("难过", Arrays.asList(
            "难过", "伤心", "痛苦", "失望", "沮丧", "郁闷", "悲伤", "心痛", "绝望",
            "😢", "😭", "💔", "😞", "😔", "哭", "泪", "眼泪", "心碎", "不开心"
        ));
        
        EMOTION_KEYWORDS.put("愤怒", Arrays.asList(
            "生气", "愤怒", "气死", "讨厌", "恨", "烦", "怒", "火大", "暴躁",
            "😡", "🤬", "😠", "💢", "气人", "可恶", "混蛋", "该死", "烦死了"
        ));
        
        EMOTION_KEYWORDS.put("担心", Arrays.asList(
            "担心", "焦虑", "紧张", "害怕", "恐惧", "不安", "忧虑", "惊慌", "恐慌",
            "😰", "😟", "😨", "😱", "怕", "紧张", "慌", "不安", "忐忑"
        ));
        
        EMOTION_KEYWORDS.put("兴奋", Arrays.asList(
            "兴奋", "激动", "太棒了", "厉害", "牛", "赞", "awesome", "cool", "great",
            "🤩", "✨", "🔥", "💪", "激动", "振奋", "热血", "澎湃", "刺激"
        ));
        
        EMOTION_KEYWORDS.put("平静", Arrays.asList(
            "平静", "还好", "一般", "普通", "正常", "淡定", "冷静", "安静", "稳定",
            "😐", "😑", "🙂", "还行", "凑合", "马马虎虎", "无所谓", "随便"
        ));
        
        EMOTION_KEYWORDS.put("关心", Arrays.asList(
            "关心", "照顾", "帮助", "支持", "爱", "想念", "思念", "牵挂", "在乎",
            "❤️", "💕", "💖", "😘", "亲爱的", "宝贝", "爱你", "想你", "关怀"
        ));
        
        EMOTION_KEYWORDS.put("感谢", Arrays.asList(
            "谢谢", "感谢", "感激", "感恩", "多谢", "谢了", "thanks", "thank you",
            "🙏", "感谢你", "太感谢了", "非常感谢", "万分感谢", "谢谢你的"
        ));
        
        // 初始化强度词汇
        INTENSITY_WORDS.put("非常", 3);
        INTENSITY_WORDS.put("特别", 3);
        INTENSITY_WORDS.put("极其", 3);
        INTENSITY_WORDS.put("超级", 3);
        INTENSITY_WORDS.put("太", 3);
        INTENSITY_WORDS.put("很", 2);
        INTENSITY_WORDS.put("比较", 2);
        INTENSITY_WORDS.put("挺", 2);
        INTENSITY_WORDS.put("还", 1);
        INTENSITY_WORDS.put("有点", 1);
        INTENSITY_WORDS.put("稍微", 1);
    }
    
    /**
     * 分析文本情感
     * 
     * @param message 待分析的消息
     * @return 情感分析结果
     */
    public EmotionAnalyzeResponse.EmotionResult analyze(String message) {
        if (message == null || message.trim().isEmpty()) {
            return createDefaultResult();
        }
        
        String text = message.toLowerCase().trim();
        
        // 计算各种情感的得分
        Map<String, Double> emotionScores = calculateEmotionScores(text);
        
        // 找出主要情感
        String primaryEmotion = findPrimaryEmotion(emotionScores);
        
        // 计算置信度
        double confidence = calculateConfidence(emotionScores, primaryEmotion);
        
        // 计算情感强度
        int intensity = calculateIntensity(text);
        
        // 生成详细情感分布
        List<EmotionAnalyzeResponse.EmotionDetail> details = generateEmotionDetails(emotionScores);
        
        // 构建结果
        EmotionAnalyzeResponse.EmotionResult result = new EmotionAnalyzeResponse.EmotionResult();
        result.setEmotion(primaryEmotion);
        result.setConfidence(confidence);
        result.setIntensity(intensity);
        result.setDetails(details);
        
        log.info("情感分析完成: 消息='{}', 情感='{}', 置信度={}, 强度={}", 
                message, primaryEmotion, confidence, intensity);
        
        return result;
    }
    
    /**
     * 计算各种情感的得分
     */
    private Map<String, Double> calculateEmotionScores(String text) {
        Map<String, Double> scores = new HashMap<>();
        
        for (Map.Entry<String, List<String>> entry : EMOTION_KEYWORDS.entrySet()) {
            String emotion = entry.getKey();
            List<String> keywords = entry.getValue();
            
            double score = 0.0;
            for (String keyword : keywords) {
                // 计算关键词在文本中的出现次数
                int count = countOccurrences(text, keyword.toLowerCase());
                if (count > 0) {
                    // 根据关键词长度和出现次数计算得分
                    double keywordScore = count * (keyword.length() > 1 ? 1.0 : 0.5);
                    score += keywordScore;
                }
            }
            
            scores.put(emotion, score);
        }
        
        return scores;
    }
    
    /**
     * 计算字符串在文本中的出现次数
     */
    private int countOccurrences(String text, String keyword) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(keyword, index)) != -1) {
            count++;
            index += keyword.length();
        }
        return count;
    }
    
    /**
     * 找出主要情感
     */
    private String findPrimaryEmotion(Map<String, Double> emotionScores) {
        return emotionScores.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("平静");
    }
    
    /**
     * 计算置信度
     */
    private double calculateConfidence(Map<String, Double> emotionScores, String primaryEmotion) {
        double maxScore = emotionScores.get(primaryEmotion);
        double totalScore = emotionScores.values().stream().mapToDouble(Double::doubleValue).sum();
        
        if (totalScore == 0) {
            return 60.0; // 默认置信度
        }
        
        // 基础置信度 = 主要情感得分占比
        double baseConfidence = (maxScore / totalScore) * 100;
        
        // 调整置信度范围到 60-95
        double adjustedConfidence = 60 + (baseConfidence * 0.35);
        
        return Math.min(95.0, Math.max(60.0, adjustedConfidence));
    }
    
    /**
     * 计算情感强度
     */
    private int calculateIntensity(String text) {
        int maxIntensity = 1;
        
        for (Map.Entry<String, Integer> entry : INTENSITY_WORDS.entrySet()) {
            if (text.contains(entry.getKey())) {
                maxIntensity = Math.max(maxIntensity, entry.getValue());
            }
        }
        
        return Math.min(5, maxIntensity);
    }
    
    /**
     * 生成详细情感分布
     */
    private List<EmotionAnalyzeResponse.EmotionDetail> generateEmotionDetails(Map<String, Double> emotionScores) {
        List<EmotionAnalyzeResponse.EmotionDetail> details = new ArrayList<>();
        double totalScore = emotionScores.values().stream().mapToDouble(Double::doubleValue).sum();
        
        if (totalScore > 0) {
            emotionScores.entrySet().stream()
                    .filter(entry -> entry.getValue() > 0)
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .forEach(entry -> {
                        EmotionAnalyzeResponse.EmotionDetail detail = new EmotionAnalyzeResponse.EmotionDetail();
                        detail.setEmotion(entry.getKey());
                        detail.setProbability(entry.getValue() / totalScore);
                        details.add(detail);
                    });
        }
        
        return details;
    }

    /**
     * 创建默认结果
     */
    private EmotionAnalyzeResponse.EmotionResult createDefaultResult() {
        EmotionAnalyzeResponse.EmotionResult result = new EmotionAnalyzeResponse.EmotionResult();
        result.setEmotion("平静");
        result.setConfidence(60.0);
        result.setIntensity(1);
        result.setDetails(new ArrayList<>());
        return result;
    }
}
