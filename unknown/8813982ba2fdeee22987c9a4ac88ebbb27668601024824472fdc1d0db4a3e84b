# 邮件配置说明

## 环境变量配置

为了使邮件功能正常工作，需要设置以下环境变量：

### QQ邮箱配置示例

```bash
# QQ邮箱SMTP配置
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_authorization_code
```

### 163邮箱配置示例

```bash
# 163邮箱SMTP配置
MAIL_HOST=smtp.163.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_authorization_code
```

### Gmail配置示例

```bash
# Gmail SMTP配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
```

## 获取邮箱授权码

### QQ邮箱
1. 登录QQ邮箱网页版
2. 点击"设置" -> "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"POP3/SMTP服务"或"IMAP/SMTP服务"
5. 按照提示获取授权码

### 163邮箱
1. 登录163邮箱网页版
2. 点击"设置" -> "POP3/SMTP/IMAP"
3. 开启"POP3/SMTP服务"
4. 设置客户端授权密码

### Gmail
1. 开启两步验证
2. 生成应用专用密码
3. 使用应用专用密码作为MAIL_PASSWORD

## 本地开发配置

在开发环境中，可以在IDE中设置环境变量，或者创建 `.env` 文件：

```bash
# .env 文件示例
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_authorization_code
```

## 测试邮件发送

启动应用后，可以通过以下方式测试邮件发送：

1. 使用忘记密码功能
2. 查看应用日志确认邮件发送状态
3. 检查收件箱（包括垃圾邮件文件夹）

## 常见问题

### 1. 邮件发送失败
- 检查邮箱授权码是否正确
- 确认SMTP服务是否已开启
- 检查网络连接

### 2. 邮件被识别为垃圾邮件
- 建议使用企业邮箱
- 配置SPF、DKIM等邮件认证
- 避免使用敏感词汇

### 3. 连接超时
- 检查防火墙设置
- 尝试使用不同的端口（25, 465, 587）
- 确认SMTP服务器地址正确

## 生产环境建议

1. 使用企业邮箱服务
2. 配置邮件发送频率限制
3. 实现邮件发送队列
4. 添加邮件发送监控和告警
5. 定期清理邮件发送日志
