# 配额管理系统设计

## 配额体系概述

### 用户分级体系
| 角色 | 每日配额 | 月费 | 特殊权限 |
|------|----------|------|----------|
| free | 10次/天 | 免费 | 基础回复风格 |
| premium | 150次/天 | ¥9.9/月 | 全部回复风格 + 优先响应 + 历史记录 |
| admin | 无限制 | - | 系统管理权限 |

## 配额管理逻辑

### 1. 配额初始化
```sql
-- 新用户注册时初始化配额
INSERT INTO users (user_id, username, email, role, daily_quota)
VALUES ('user_123', 'testuser', '<EMAIL>', 'free', 10);
```

### 2. 每日配额重置
```sql
-- 每日凌晨重置所有用户的使用次数
UPDATE users SET 
    used_today = 0,
    last_used_date = CURDATE()
WHERE last_used_date < CURDATE();
```

### 3. 使用次数检查和扣减
```java
public class QuotaService {
    
    /**
     * 检查用户是否有可用配额
     */
    public boolean checkQuota(String userId) {
        User user = userService.getByUserId(userId);
        
        // 检查是否需要重置今日使用次数
        if (!DateUtil.isSameDay(user.getLastUsedDate(), new Date())) {
            resetDailyUsage(userId);
            user.setUsedToday(0);
        }
        
        // 管理员无限制
        if ("admin".equals(user.getRole())) {
            return true;
        }
        
        // 检查配额
        return user.getUsedToday() < user.getDailyQuota();
    }
    
    /**
     * 扣减配额
     */
    public boolean consumeQuota(String userId) {
        if (!checkQuota(userId)) {
            // 记录超额尝试
            recordQuotaExceeded(userId);
            return false;
        }
        
        // 扣减配额
        userMapper.incrementUsedToday(userId);
        userMapper.incrementTotalUsed(userId);
        
        // 更新统计
        updateUsageStatistics(userId);
        
        return true;
    }
    
    /**
     * 重置每日使用次数
     */
    private void resetDailyUsage(String userId) {
        userMapper.resetDailyUsage(userId);
    }
    
    /**
     * 记录超额尝试
     */
    private void recordQuotaExceeded(String userId) {
        UsageStatistics stats = getOrCreateTodayStats(userId);
        stats.setQuotaExceededCount(stats.getQuotaExceededCount() + 1);
        usageStatisticsMapper.updateById(stats);
    }
}
```

### 4. 会员升级逻辑
```java
public class MembershipService {
    
    /**
     * 升级会员 (通过激活码)
     */
    public boolean upgradeMembershipByCode(String userId, String activationCode) {
        // 验证并使用激活码
        ActivationCode code = activationCodeService.validateAndUseCode(userId, activationCode);
        if (code == null) {
            return false;
        }

        User user = userService.getByUserId(userId);

        // 计算新的到期时间
        Date newExpireTime = calculateNewExpireTime(user, code);

        // 设置为高级会员
        user.setRole("premium");
        user.setDailyQuota(150);
        user.setPremiumExpire(newExpireTime);

        return userService.updateById(user);
    }

    /**
     * 升级会员 (直接付费)
     */
    public boolean upgradeMembershipByPayment(String userId, String role, int months) {
        User user = userService.getByUserId(userId);

        // 设置新的角色和配额
        int newQuota = getQuotaByRole(role);
        Date expireDate = calculateExpireDate(months);

        user.setRole(role);
        user.setDailyQuota(newQuota);
        user.setPremiumExpire(expireDate);

        return userService.updateById(user);
    }
    
    /**
     * 检查会员是否过期
     */
    public void checkMembershipExpiry() {
        List<User> expiredUsers = userMapper.selectExpiredMembers();
        
        for (User user : expiredUsers) {
            // 降级为免费用户
            user.setRole("free");
            user.setDailyQuota(10);
            user.setPremiumExpire(null);

            userService.updateById(user);

            // 发送过期通知
            notificationService.sendMembershipExpiredNotice(user);
        }
    }
    
    private int getQuotaByRole(String role) {
        switch (role) {
            case "free": return 10;
            case "premium": return 150;
            case "admin": return Integer.MAX_VALUE;
            default: return 10;
        }
    }
}
```

## 配额监控和统计

### 1. 实时配额监控
```java
@RestController
@RequestMapping("/api/quota")
public class QuotaController {
    
    /**
     * 获取用户配额信息
     */
    @GetMapping("/info")
    public Result<QuotaInfo> getQuotaInfo(@RequestParam String userId) {
        User user = userService.getByUserId(userId);
        
        QuotaInfo quotaInfo = new QuotaInfo();
        quotaInfo.setDailyQuota(user.getDailyQuota());
        quotaInfo.setUsedToday(user.getUsedToday());
        quotaInfo.setRemainingToday(user.getDailyQuota() - user.getUsedToday());
        quotaInfo.setRole(user.getRole());
        quotaInfo.setPremiumExpire(user.getPremiumExpire());
        
        return Result.success(quotaInfo);
    }
    
    /**
     * 获取使用统计
     */
    @GetMapping("/statistics")
    public Result<List<UsageStatistics>> getUsageStatistics(
            @RequestParam String userId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        
        List<UsageStatistics> statistics = usageStatisticsService
            .getStatisticsByDateRange(userId, startDate, endDate);
        
        return Result.success(statistics);
    }
}
```

### 2. 配额预警机制
```java
@Component
public class QuotaAlertService {
    
    /**
     * 检查配额使用情况并发送预警
     */
    @Scheduled(cron = "0 0 */2 * * ?") // 每2小时检查一次
    public void checkQuotaUsage() {
        List<User> users = userService.getAllActiveUsers();
        
        for (User user : users) {
            double usageRate = (double) user.getUsedToday() / user.getDailyQuota();
            
            // 使用率超过80%时发送预警
            if (usageRate >= 0.8 && usageRate < 1.0) {
                sendQuotaWarning(user, usageRate);
            }
            // 配额用完时发送通知
            else if (usageRate >= 1.0) {
                sendQuotaExhaustedNotice(user);
            }
        }
    }
    
    private void sendQuotaWarning(User user, double usageRate) {
        String message = String.format(
            "您今日的使用配额已达到%.0f%%，剩余%d次使用机会",
            usageRate * 100,
            user.getDailyQuota() - user.getUsedToday()
        );
        
        notificationService.sendNotification(user.getUserId(), message);
    }
    
    private void sendQuotaExhaustedNotice(User user) {
        String message = "您今日的使用配额已用完，请明日再试或升级会员获得更多配额";
        notificationService.sendNotification(user.getUserId(), message);
    }
}
```

## 数据库优化

### 1. 索引优化
```sql
-- 用户表索引
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_last_used_date ON users(last_used_date);
CREATE INDEX idx_users_premium_expire ON users(premium_expire);

-- 统计表索引
CREATE INDEX idx_usage_stats_user_date ON usage_statistics(user_id, date);
CREATE INDEX idx_usage_stats_date_quota ON usage_statistics(date, daily_quota);
```

### 2. 定期数据清理
```sql
-- 清理90天前的使用统计数据
DELETE FROM usage_statistics 
WHERE date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);

-- 清理已删除用户的统计数据
DELETE us FROM usage_statistics us
LEFT JOIN users u ON us.user_id = u.user_id
WHERE u.user_id IS NULL;
```

## 业务规则

### 1. 配额使用规则
- 每次成功生成回复扣减1次配额
- 生成失败不扣减配额
- 管理员账户不受配额限制
- 配额每日凌晨自动重置

### 2. 会员权益
- 免费用户：基础回复风格，50次/天
- 高级会员：全部回复风格，300次/天，优先响应，历史记录

### 3. 超额处理
- 免费用户超额：提示升级会员
- 付费用户超额：软限制，允许少量超额使用
- 恶意刷量：自动封禁账户

### 4. 异常处理
- 系统故障时不扣减配额
- 网络超时重试不重复扣减
- 用户取消操作可退还配额

## 监控指标

### 1. 关键指标
- 日活跃用户数 (DAU)
- 平均每用户使用次数
- 配额使用率分布
- 会员转化率
- 超额尝试次数

### 2. 报警阈值
- 系统整体配额使用率 > 85%
- 单用户异常高频使用 (>正常用户10倍)
- 大量用户同时超额 (>100人/小时)
- 会员过期未续费率 > 50%
