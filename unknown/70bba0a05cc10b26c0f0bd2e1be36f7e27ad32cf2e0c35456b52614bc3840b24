# 情感回复助手数据库设置指南

## 📋 **数据库设置步骤**

### **1. 环境要求**
- MySQL 8.0+ 或 MariaDB 10.5+
- 确保MySQL服务正在运行
- 具有创建数据库的权限

### **2. 创建数据库**

#### **方法一：使用MySQL命令行**
```bash
# 登录MySQL
mysql -u root -p

# 执行初始化脚本
source /path/to/EmotionalReplyApp/backend-service/src/main/resources/sql/init.sql

# 执行数据初始化脚本
source /path/to/EmotionalReplyApp/backend-service/src/main/resources/sql/data.sql
```

#### **方法二：使用MySQL Workbench**
1. 打开MySQL Workbench
2. 连接到MySQL服务器
3. 打开 `init.sql` 文件并执行
4. 打开 `data.sql` 文件并执行

#### **方法三：使用命令行直接执行**
```bash
# Windows
mysql -u root -p < "EmotionalReplyApp\backend-service\src\main\resources\sql\init.sql"
mysql -u root -p < "EmotionalReplyApp\backend-service\src\main\resources\sql\data.sql"

# Linux/Mac
mysql -u root -p < EmotionalReplyApp/backend-service/src/main/resources/sql/init.sql
mysql -u root -p < EmotionalReplyApp/backend-service/src/main/resources/sql/data.sql
```

### **3. 验证安装**

执行测试脚本验证数据库是否正确创建：
```bash
mysql -u root -p < EmotionalReplyApp/backend-service/src/main/resources/sql/test.sql
```

### **4. 配置应用连接**

确保 `application.yml` 中的数据库配置正确：
```yaml
spring:
  datasource:
    url: *******************************************************************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

## 🗄️ **数据库结构说明**

### **核心表**

#### **1. users（用户表）**
- 存储用户基本信息
- 包含VIP状态、配额管理
- 支持逻辑删除

#### **2. reply_history（回复历史表）**
- 存储所有的回复生成记录
- 包含情感分析结果和用户反馈
- 支持收藏功能

#### **3. user_settings（用户设置表）**
- 存储用户个性化设置
- 主题、字体、通知等配置

#### **4. system_config（系统配置表）**
- 存储系统级配置参数
- 支持动态配置更新

#### **5. emotion_statistics（情感统计表）**
- 存储情感分析统计数据
- 支持用户级和全局统计

#### **6. api_call_log（API调用日志表）**
- 记录所有API调用
- 用于性能监控和问题排查

### **索引设计**
- 主键索引：所有表都有自增主键
- 外键索引：维护数据完整性
- 业务索引：优化常用查询性能
- 复合索引：优化复杂查询

### **数据类型说明**
- 使用 `utf8mb4` 字符集支持emoji
- 使用 `JSON` 类型存储复杂数据
- 使用 `DECIMAL` 存储精确数值
- 使用 `DATETIME` 存储时间信息

## 🔧 **维护操作**

### **备份数据库**
```bash
mysqldump -u root -p emotional_reply_db > backup_$(date +%Y%m%d_%H%M%S).sql
```

### **恢复数据库**
```bash
mysql -u root -p emotional_reply_db < backup_file.sql
```

### **清理测试数据**
```sql
-- 清理测试用户数据（保留结构）
DELETE FROM reply_history WHERE user_id IN (1, 2, 3);
DELETE FROM user_settings WHERE user_id IN (1, 2, 3);
DELETE FROM users WHERE id IN (1, 2, 3);
DELETE FROM emotion_statistics WHERE user_id IN (1, 2, 3);
DELETE FROM api_call_log WHERE user_id IN (1, 2, 3);
```

### **重置自增ID**
```sql
ALTER TABLE users AUTO_INCREMENT = 1;
ALTER TABLE reply_history AUTO_INCREMENT = 1;
ALTER TABLE user_settings AUTO_INCREMENT = 1;
ALTER TABLE system_config AUTO_INCREMENT = 1;
ALTER TABLE emotion_statistics AUTO_INCREMENT = 1;
ALTER TABLE api_call_log AUTO_INCREMENT = 1;
```

## 📊 **性能优化建议**

### **1. 索引优化**
- 定期分析慢查询日志
- 根据实际查询模式调整索引
- 避免过多的复合索引

### **2. 数据清理**
- 定期清理过期的API调用日志
- 归档历史数据
- 清理逻辑删除的数据

### **3. 分区策略**
对于大数据量表，可以考虑按时间分区：
```sql
-- 示例：按月分区reply_history表
ALTER TABLE reply_history PARTITION BY RANGE (YEAR(create_time)*100 + MONTH(create_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ... 更多分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 🚨 **注意事项**

1. **密码安全**：示例数据中的密码是加密后的测试密码
2. **权限控制**：生产环境应使用专门的数据库用户
3. **备份策略**：建议设置定期自动备份
4. **监控告警**：设置数据库性能监控
5. **版本控制**：数据库结构变更应纳入版本控制

## 📈 **扩展建议**

1. **读写分离**：高并发时可考虑主从复制
2. **缓存层**：使用Redis缓存热点数据
3. **分库分表**：用户量大时可按用户ID分表
4. **数据同步**：与其他系统的数据同步策略
