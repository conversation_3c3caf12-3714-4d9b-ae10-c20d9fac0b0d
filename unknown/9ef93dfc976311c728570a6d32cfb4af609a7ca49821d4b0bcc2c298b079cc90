# 情感回复助手 - 部署指南

## 🏗️ 前后端分离架构

本项目采用前后端分离架构，支持多种部署方式：

### 📱 前端部分
- **uni-app H5版本**: 部署到 Web 服务器
- **uni-app App版本**: 发布到应用商店
- **微信小程序**: 发布到微信平台

### 🌐 后端部分
- **Spring Boot API**: 独立的 RESTful 服务
- **MySQL 数据库**: 数据存储
- **Redis 缓存**: 性能优化

## 🚀 部署方案

### 方案一：单服务器部署 (推荐小型项目)

**服务器配置要求:**
- CPU: 2核心
- 内存: 4GB
- 存储: 50GB SSD
- 带宽: 5Mbps

**架构图:**
```
🖥️ 云服务器
├── 🌐 Nginx (端口 80/443)
│   ├── 静态文件服务 (H5应用)
│   └── 反向代理 (API请求)
├── ☕ Spring Boot (端口 8080)
├── 🗄️ MySQL (端口 3306)
└── 📦 Redis (端口 6379)
```

### 方案二：Docker 容器化部署 (推荐)

**优势:**
- 环境一致性
- 快速部署
- 易于扩展
- 服务隔离

### 方案三：分布式部署 (生产环境)

**架构图:**
```
🌍 负载均衡器
├── 📱 前端服务器集群
├── ☕ 后端服务器集群
├── 🗄️ 数据库主从集群
└── 📦 Redis 集群
```

## 📋 部署步骤

### 1. 环境准备

#### 系统要求
- Ubuntu 20.04+ / CentOS 8+
- Docker 20.10+ (Docker部署)
- Node.js 16+
- Java 8+
- MySQL 8.0+
- Nginx 1.18+

#### 安装依赖
```bash
# Ubuntu
sudo apt update
sudo apt install -y nginx mysql-server redis-server openjdk-8-jdk nodejs npm

# CentOS
sudo yum update
sudo yum install -y nginx mysql-server redis openjdk-8-jdk nodejs npm
```

### 2. 克隆项目
```bash
git clone https://github.com/your-username/EmotionalReplyApp.git
cd EmotionalReplyApp
```

### 3. 配置环境变量
```bash
cd deploy
cp .env.example .env
# 编辑 .env 文件，填入实际配置
vim .env
```

### 4. 一键部署

#### Docker 部署 (推荐)
```bash
# 给脚本执行权限
chmod +x deploy-all.sh

# 执行部署
./deploy-all.sh prod docker
```

#### 传统部署
```bash
# 执行部署
./deploy-all.sh prod traditional
```

### 5. 验证部署

访问以下地址验证部署结果：
- 前端应用: http://your-domain.com
- 后端API: http://your-domain.com/api/actuator/health
- 监控面板: http://your-domain.com:3000 (Docker部署)

## 🔧 配置说明

### Nginx 配置
- 静态文件服务
- API 反向代理
- SSL 证书配置
- Gzip 压缩
- 缓存策略

### 后端配置
- 数据库连接
- Redis 缓存
- JWT 安全
- 日志配置
- 监控指标

### 数据库配置
- 连接池设置
- 字符集配置
- 索引优化
- 备份策略

## 📊 监控和运维

### 日志管理
```bash
# 查看后端日志
sudo journalctl -u emotional-reply -f

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/emotional-reply.access.log

# Docker 日志
docker-compose logs -f backend
```

### 性能监控
- **Prometheus**: 指标收集
- **Grafana**: 可视化面板
- **Spring Boot Actuator**: 应用监控

### 备份策略
```bash
# 数据库备份
mysqldump -u root -p emotional_reply > backup_$(date +%Y%m%d).sql

# 应用备份
tar -czf app_backup_$(date +%Y%m%d).tar.gz /opt/emotional-reply
```

## 🔄 更新部署

### 后端更新
```bash
cd deploy
./backend-deploy.sh prod 1.1.0
```

### 前端更新
```bash
cd deploy
./frontend-deploy.sh prod h5
```

### Docker 更新
```bash
cd deploy
docker-compose down
docker-compose up -d --build
```

## 🛡️ 安全配置

### SSL 证书
```bash
# 使用 Let's Encrypt
sudo certbot --nginx -d your-domain.com
```

### 防火墙设置
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 数据库安全
```bash
# MySQL 安全配置
sudo mysql_secure_installation
```

## 🚨 故障排除

### 常见问题

#### 1. 后端服务启动失败
```bash
# 检查日志
sudo journalctl -u emotional-reply -n 50

# 检查端口占用
sudo netstat -tlnp | grep 8080
```

#### 2. 前端页面无法访问
```bash
# 检查 Nginx 状态
sudo systemctl status nginx

# 检查配置语法
sudo nginx -t
```

#### 3. 数据库连接失败
```bash
# 检查 MySQL 状态
sudo systemctl status mysql

# 测试连接
mysql -u emotional_reply -p -h localhost
```

### 回滚操作
```bash
# 回滚后端
sudo systemctl stop emotional-reply
sudo cp /opt/emotional-reply/emotional-reply-service-latest.jar.backup.* /opt/emotional-reply/emotional-reply-service-latest.jar
sudo systemctl start emotional-reply

# 回滚前端
sudo cp -r /var/backups/emotional-reply/backup_* /var/www/emotional-reply/h5/
sudo systemctl reload nginx
```

## 📈 性能优化

### 数据库优化
- 索引优化
- 查询优化
- 连接池调优

### 缓存策略
- Redis 缓存
- Nginx 静态文件缓存
- 浏览器缓存

### 负载均衡
- Nginx 负载均衡
- 数据库读写分离
- CDN 加速

## 📞 技术支持

如遇到部署问题，请：
1. 查看相关日志文件
2. 检查配置文件
3. 提交 Issue 并附上错误信息
4. 联系技术支持团队

---

**部署成功后，您的情感回复助手应用就可以为用户提供服务了！** 🎉
