package com.emotional.service.service.impl;

import com.emotional.service.dto.EmotionAnalyzeRequest;
import com.emotional.service.dto.EmotionAnalyzeResponse;
import com.emotional.service.entity.ReplyHistory;
import com.emotional.service.service.EmotionService;
import com.emotional.service.service.ReplyHistoryService;
import com.emotional.service.util.EmotionAnalyzer;
import com.emotional.service.util.ReplyGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 情感分析服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmotionServiceImpl implements EmotionService {
    
    private final ReplyHistoryService replyHistoryService;
    private final EmotionAnalyzer emotionAnalyzer;
    private final ReplyGenerator replyGenerator;
    
    @Override
    public EmotionAnalyzeResponse analyzeAndGenerateReply(EmotionAnalyzeRequest request, 
                                                         String clientIp, 
                                                         String userAgent) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 情感分析
            log.info("开始情感分析: {}", request.getMessage());
            EmotionAnalyzeResponse.EmotionResult emotionResult = analyzeEmotion(request.getMessage());
            
            // 2. 生成回复
            log.info("开始生成回复，检测到情感: {}", emotionResult.getEmotion());
            List<EmotionAnalyzeResponse.ReplyOption> replyOptions = generateReplies(
                request.getMessage(), emotionResult, request.getReplyStyles());
            
            // 3. 保存历史记录
            Long historyId = null;
            if (request.getSaveHistory()) {
                historyId = saveReplyHistory(request, emotionResult, replyOptions, 
                                           clientIp, userAgent, startTime);
            }
            
            // 4. 构建响应
            EmotionAnalyzeResponse response = new EmotionAnalyzeResponse();
            response.setHistoryId(historyId);
            response.setOriginalMessage(request.getMessage());
            response.setEmotionResult(emotionResult);
            response.setReplyOptions(replyOptions);
            response.setProcessTime(System.currentTimeMillis() - startTime);
            
            log.info("情感分析和回复生成完成，耗时: {}ms", response.getProcessTime());
            return response;
            
        } catch (Exception e) {
            log.error("情感分析和回复生成失败", e);
            throw new RuntimeException("处理失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public EmotionAnalyzeResponse regenerateReply(Long historyId, EmotionAnalyzeRequest request) {
        // 获取历史记录
        ReplyHistory history = replyHistoryService.getById(historyId);
        if (history == null) {
            throw new RuntimeException("历史记录不存在");
        }
        
        // 使用历史记录中的消息重新生成
        EmotionAnalyzeRequest newRequest = new EmotionAnalyzeRequest();
        newRequest.setMessage(history.getOriginalMessage());
        newRequest.setReplyStyles(request != null ? request.getReplyStyles() : null);
        newRequest.setSaveHistory(false); // 不保存新的历史记录
        
        return analyzeAndGenerateReply(newRequest, null, null);
    }
    
    @Override
    public Map<String, String> getReplyStyles() {
        Map<String, String> styles = new LinkedHashMap<>();
        styles.put("warm_caring", "温暖关怀");
        styles.put("humorous", "幽默风趣");
        styles.put("rational", "理性分析");
        styles.put("concise", "简洁直接");
        styles.put("romantic", "浪漫情话");
        styles.put("encouraging", "鼓励支持");
        styles.put("professional", "专业正式");
        styles.put("casual", "轻松随意");
        return styles;
    }
    
    @Override
    public EmotionAnalyzeResponse.EmotionResult analyzeEmotion(String message) {
        return emotionAnalyzer.analyze(message);
    }
    
    @Override
    public List<EmotionAnalyzeResponse.ReplyOption> generateReplies(String message, 
                                                                   EmotionAnalyzeResponse.EmotionResult emotionResult,
                                                                   List<String> replyStyles) {
        // 如果没有指定风格，使用默认风格
        if (replyStyles == null || replyStyles.isEmpty()) {
            replyStyles = Arrays.asList("warm_caring", "humorous", "rational", "concise", "romantic");
        }
        
        return replyGenerator.generateReplies(message, emotionResult, replyStyles);
    }
    
    /**
     * 保存回复历史记录
     */
    private Long saveReplyHistory(EmotionAnalyzeRequest request,
                                 EmotionAnalyzeResponse.EmotionResult emotionResult,
                                 List<EmotionAnalyzeResponse.ReplyOption> replyOptions,
                                 String clientIp,
                                 String userAgent,
                                 long startTime) {
        try {
            ReplyHistory history = new ReplyHistory();
            history.setUserId(1L); // 暂时使用默认用户ID，后续集成用户系统
            history.setOriginalMessage(request.getMessage());
            history.setEmotionResult(emotionResult.getEmotion());
            history.setEmotionConfidence(emotionResult.getConfidence());
            
            // 将回复列表转换为JSON字符串保存
            history.setReplyList(convertReplyOptionsToJson(replyOptions));
            
            history.setIsFavorite(0);
            history.setProcessTime(System.currentTimeMillis() - startTime);
            history.setClientIp(clientIp);
            history.setUserAgent(userAgent);
            
            replyHistoryService.save(history);
            return history.getId();
            
        } catch (Exception e) {
            log.error("保存历史记录失败", e);
            // 不抛出异常，避免影响主流程
            return null;
        }
    }
    
    /**
     * 将回复选项转换为JSON字符串
     */
    private String convertReplyOptionsToJson(List<EmotionAnalyzeResponse.ReplyOption> replyOptions) {
        // 这里可以使用Jackson或FastJSON进行序列化
        // 暂时返回简单的字符串表示
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < replyOptions.size(); i++) {
            if (i > 0) sb.append(",");
            EmotionAnalyzeResponse.ReplyOption option = replyOptions.get(i);
            sb.append("{")
              .append("\"content\":\"").append(option.getContent()).append("\",")
              .append("\"style\":\"").append(option.getStyle()).append("\",")
              .append("\"styleName\":\"").append(option.getStyleName()).append("\"")
              .append("}");
        }
        sb.append("]");
        return sb.toString();
    }
}
