# 管理员权限使用指南

## 概述

本项目实现了完整的管理员权限管理系统，支持多级权限控制和自动权限验证。

## 数据库设计

### 用户表字段

| 字段名 | 类型 | 说明 | 取值 |
|--------|------|------|------|
| is_admin | TINYINT | 管理员标志 | 0-普通用户，1-管理员 |

### 权限级别

1. **普通用户** - `is_admin = 0`
2. **管理员** - `is_admin = 1`
3. **超级管理员** - `is_admin = 1` 且 `username = 'admin'`

## 使用方法

### 1. 简单权限判断

```java
import com.emotional.service.utils.AdminUtils;

// 判断用户是否为管理员
boolean isAdmin = AdminUtils.isAdmin(user);

// 判断用户是否为超级管理员
boolean isSuperAdmin = AdminUtils.isSuperAdmin(user);

// 获取用户权限描述
String roleDesc = AdminUtils.getUserRoleDescription(user);
```

### 2. 方法级权限控制

```java
import com.emotional.service.annotation.RequireAdmin;

@RestController
public class AdminController {
    
    // 普通管理员权限
    @RequireAdmin("用户管理")
    @PostMapping("/users/{id}/disable")
    public Result<String> disableUser(@PathVariable Long id) {
        // 只有管理员才能执行
        return Result.success("用户已禁用");
    }
    
    // 超级管理员权限
    @RequireAdmin(level = AdminLevel.SUPER_ADMIN, value = "系统配置")
    @PostMapping("/system/config")
    public Result<String> updateSystemConfig() {
        // 只有超级管理员才能执行
        return Result.success("配置已更新");
    }
}
```

### 3. 手动权限检查

```java
import com.emotional.service.utils.AdminUtils;

public void someBusinessMethod(User user) {
    // 检查权限并抛出异常
    AdminUtils.requireAdmin(user);
    
    // 或者检查权限并返回布尔值
    if (AdminUtils.hasAdminAccess(user, "数据导出")) {
        // 执行管理操作
        exportData();
    } else {
        throw new RuntimeException("权限不足");
    }
}
```

## 管理员接口

### 系统统计

```http
GET /api/admin/stats
Authorization: Bearer {token}
```

### 用户管理

```http
# 获取用户列表
GET /api/admin/users?page=1&size=20
Authorization: Bearer {token}

# 设置VIP状态
POST /api/admin/users/{userId}/vip?isVip=true
Authorization: Bearer {token}

# 设置管理员状态（需要超级管理员权限）
POST /api/admin/users/{userId}/admin?isAdmin=true
Authorization: Bearer {token}

# 禁用/启用用户
POST /api/admin/users/{userId}/status?status=1
Authorization: Bearer {token}

# 重置密码
POST /api/admin/users/{userId}/reset-password
Authorization: Bearer {token}
```

## 数据库升级

### 执行升级脚本

```sql
-- 运行升级脚本添加管理员字段
source /path/to/add_admin_field.sql;
```

### 手动添加管理员

```sql
-- 设置现有用户为管理员
UPDATE users SET is_admin = 1 WHERE username = 'admin';

-- 创建新的管理员用户
INSERT INTO users (username, nickname, email, password, is_admin, daily_quota) 
VALUES ('admin', '系统管理员', '<EMAIL>', '$2a$10$...', 1, 1000);
```

## 安全特性

### 1. 多重验证
- 检查用户状态（是否被禁用）
- 检查逻辑删除标志
- 检查管理员标志
- 验证JWT令牌

### 2. 日志记录
- 详细记录所有管理操作
- 记录权限检查失败的尝试
- 包含用户信息和操作描述

### 3. 异常处理
- 权限不足时抛出明确异常
- 统一的错误响应格式
- 全局异常处理器

### 4. AOP拦截
- 自动拦截带有@RequireAdmin注解的方法
- 支持不同权限级别
- 自动从请求中提取和验证JWT令牌

## 前端集成

### 权限检查

```javascript
// 检查当前用户权限
const checkAdminPermission = async () => {
  try {
    const response = await get('/admin/check-permission');
    return response.isAdmin;
  } catch (error) {
    return false;
  }
};

// 根据权限显示/隐藏功能
if (await checkAdminPermission()) {
  // 显示管理员功能
  showAdminPanel();
}
```

### 请求头设置

```javascript
// 在请求中包含JWT令牌
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};
```

## 常见问题

### Q: 如何添加新的管理员？
A: 有两种方式：
1. 通过超级管理员在系统中设置
2. 直接在数据库中将用户的`is_admin`字段设置为1

### Q: 如何区分普通管理员和超级管理员？
A: 超级管理员是用户名为"admin"或"superadmin"的管理员用户

### Q: 权限检查失败会发生什么？
A: 系统会抛出RuntimeException，全局异常处理器会返回统一的错误响应

### Q: 如何自定义权限级别？
A: 可以扩展AdminLevel枚举，并在AdminUtils中添加相应的判断逻辑

## 最佳实践

1. **最小权限原则** - 只给用户必要的权限
2. **定期审计** - 定期检查管理员账号的使用情况
3. **日志监控** - 监控管理员操作日志，及时发现异常
4. **密码安全** - 管理员账号使用强密码，定期更换
5. **权限分离** - 区分不同级别的管理员权限

## 扩展建议

1. **角色管理** - 可以扩展为基于角色的权限控制（RBAC）
2. **权限缓存** - 对频繁的权限检查进行缓存优化
3. **操作审计** - 记录更详细的操作审计日志
4. **权限继承** - 支持权限的继承和委托机制
