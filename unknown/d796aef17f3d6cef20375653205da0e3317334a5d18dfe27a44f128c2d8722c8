/**
 * 回复生成相关 API
 */
import { post, get } from '../utils/request.js'

/**
 * 生成回复
 * @param {string} message - 原始消息
 * @param {object} options - 生成选项
 * @returns {Promise} 生成的回复列表
 */
export const generateReply = (message, options = {}) => {
  return post('/reply/generate', {
    message,
    options: {
      styles: ['warm_caring', 'humorous', 'rational', 'concise', 'romantic'],
      maxLength: 200,
      minLength: 10,
      count: 5,
      emotionContext: null,
      ...options
    }
  })
}

/**
 * 根据风格生成回复
 * @param {string} message - 原始消息
 * @param {string} style - 回复风格
 * @param {object} options - 其他选项
 * @returns {Promise} 生成的回复
 */
export const generateReplyByStyle = (message, style, options = {}) => {
  return post('/reply/generate-by-style', {
    message,
    style,
    options: {
      maxLength: 200,
      minLength: 10,
      emotionContext: null,
      ...options
    }
  })
}

/**
 * 优化回复内容
 * @param {string} reply - 原始回复
 * @param {object} options - 优化选项
 * @returns {Promise} 优化后的回复
 */
export const optimizeReply = (reply, options = {}) => {
  return post('/reply/optimize', {
    reply,
    options: {
      targetLength: null,
      targetStyle: null,
      addEmoji: true,
      ...options
    }
  })
}

/**
 * 评价回复质量
 * @param {string} originalMessage - 原始消息
 * @param {string} reply - 回复内容
 * @param {number} rating - 评分 (1-5)
 * @param {string} feedback - 反馈内容
 * @returns {Promise} 评价结果
 */
export const rateReply = (originalMessage, reply, rating, feedback = '') => {
  return post('/reply/rate', {
    originalMessage,
    reply,
    rating,
    feedback
  })
}

/**
 * 获取回复历史
 * @param {object} params - 查询参数
 * @returns {Promise} 回复历史
 */
export const getReplyHistory = (params = {}) => {
  return get('/reply/history', {
    page: 1,
    size: 20,
    style: null,
    startDate: null,
    endDate: null,
    ...params
  })
}

/**
 * 收藏回复
 * @param {string} replyId - 回复ID
 * @returns {Promise} 收藏结果
 */
export const favoriteReply = (replyId) => {
  return post('/reply/favorite', {
    replyId
  })
}

/**
 * 取消收藏回复
 * @param {string} replyId - 回复ID
 * @returns {Promise} 取消收藏结果
 */
export const unfavoriteReply = (replyId) => {
  return post('/reply/unfavorite', {
    replyId
  })
}

/**
 * 获取收藏的回复
 * @param {object} params - 查询参数
 * @returns {Promise} 收藏的回复列表
 */
export const getFavoriteReplies = (params = {}) => {
  return get('/reply/favorites', {
    page: 1,
    size: 20,
    ...params
  })
}

/**
 * 获取回复风格列表
 * @returns {Promise} 风格列表
 */
export const getReplyStyles = () => {
  return get('/reply/styles')
}

/**
 * 获取回复模板
 * @param {string} category - 模板分类
 * @returns {Promise} 模板列表
 */
export const getReplyTemplates = (category = 'all') => {
  return get('/reply/templates', {
    category
  })
}

/**
 * 创建自定义回复模板
 * @param {object} template - 模板数据
 * @returns {Promise} 创建结果
 */
export const createReplyTemplate = (template) => {
  return post('/reply/templates', template)
}

/**
 * 获取回复统计数据
 * @param {string} timeRange - 时间范围
 * @returns {Promise} 统计数据
 */
export const getReplyStatistics = (timeRange = 'week') => {
  return get('/reply/statistics', {
    timeRange
  })
}

export default {
  generateReply,
  generateReplyByStyle,
  optimizeReply,
  rateReply,
  getReplyHistory,
  favoriteReply,
  unfavoriteReply,
  getFavoriteReplies,
  getReplyStyles,
  getReplyTemplates,
  createReplyTemplate,
  getReplyStatistics
}
