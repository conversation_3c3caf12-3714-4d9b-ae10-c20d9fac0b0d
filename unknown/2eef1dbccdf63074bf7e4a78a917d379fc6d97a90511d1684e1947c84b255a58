-- 数据库连接和功能测试脚本
-- 创建时间: 2024-01-15
-- 作者: YUMU

USE emotional_reply_db;

-- 1. 测试用户表查询
SELECT '=== 用户表测试 ===' AS test_section;
SELECT id, username, nickname, email, daily_quota, today_used, total_used, is_vip, status, create_time 
FROM users 
ORDER BY id;

-- 2. 测试回复历史表查询
SELECT '=== 回复历史表测试 ===' AS test_section;
SELECT h.id, h.user_id, u.username, h.original_message, h.emotion_result, 
       h.emotion_confidence, h.selected_style, h.is_favorite, h.create_time
FROM reply_history h
LEFT JOIN users u ON h.user_id = u.id
ORDER BY h.create_time DESC
LIMIT 10;

-- 3. 测试用户设置表查询
SELECT '=== 用户设置表测试 ===' AS test_section;
SELECT s.user_id, u.username, s.theme, s.font_size, s.auto_save, 
       s.show_floating_bubble, s.reply_styles
FROM user_settings s
LEFT JOIN users u ON s.user_id = u.id
ORDER BY s.user_id;

-- 4. 测试系统配置表查询
SELECT '=== 系统配置表测试 ===' AS test_section;
SELECT config_key, config_value, config_desc, config_type, is_public
FROM system_config
WHERE is_public = 1
ORDER BY config_key;

-- 5. 测试情感统计查询
SELECT '=== 情感统计测试 ===' AS test_section;
-- 用户情感分布
SELECT u.username, es.emotion, es.count, es.date
FROM emotion_statistics es
LEFT JOIN users u ON es.user_id = u.id
WHERE es.user_id IS NOT NULL
ORDER BY es.user_id, es.date DESC, es.count DESC;

-- 全局情感分布
SELECT emotion, SUM(count) as total_count
FROM emotion_statistics
WHERE user_id IS NULL
GROUP BY emotion
ORDER BY total_count DESC;

-- 6. 测试API调用日志查询
SELECT '=== API调用日志测试 ===' AS test_section;
SELECT l.user_id, u.username, l.api_path, l.http_method, 
       l.response_code, l.response_time, l.create_time
FROM api_call_log l
LEFT JOIN users u ON l.user_id = u.id
ORDER BY l.create_time DESC
LIMIT 10;

-- 7. 测试复杂查询 - 用户使用统计
SELECT '=== 用户使用统计测试 ===' AS test_section;
SELECT 
    u.id,
    u.username,
    u.nickname,
    u.daily_quota,
    u.today_used,
    u.total_used,
    ROUND((u.today_used / u.daily_quota) * 100, 2) as usage_percentage,
    COUNT(h.id) as history_count,
    COUNT(CASE WHEN h.is_favorite = 1 THEN 1 END) as favorite_count,
    AVG(h.emotion_confidence) as avg_confidence,
    u.last_login_time
FROM users u
LEFT JOIN reply_history h ON u.id = h.user_id AND h.deleted = 0
WHERE u.deleted = 0
GROUP BY u.id
ORDER BY u.total_used DESC;

-- 8. 测试情感分析准确性统计
SELECT '=== 情感分析准确性统计 ===' AS test_section;
SELECT 
    emotion_result,
    COUNT(*) as total_count,
    AVG(emotion_confidence) as avg_confidence,
    MIN(emotion_confidence) as min_confidence,
    MAX(emotion_confidence) as max_confidence,
    COUNT(CASE WHEN user_rating >= 4 THEN 1 END) as high_rating_count
FROM reply_history
WHERE deleted = 0 AND emotion_result IS NOT NULL
GROUP BY emotion_result
ORDER BY total_count DESC;

-- 9. 测试回复风格偏好统计
SELECT '=== 回复风格偏好统计 ===' AS test_section;
SELECT 
    selected_style,
    COUNT(*) as usage_count,
    COUNT(DISTINCT user_id) as user_count,
    AVG(user_rating) as avg_rating
FROM reply_history
WHERE deleted = 0 AND selected_style IS NOT NULL
GROUP BY selected_style
ORDER BY usage_count DESC;

-- 10. 测试最近活跃用户
SELECT '=== 最近活跃用户测试 ===' AS test_section;
SELECT 
    u.username,
    u.nickname,
    COUNT(h.id) as recent_usage,
    MAX(h.create_time) as last_usage_time,
    u.last_login_time
FROM users u
LEFT JOIN reply_history h ON u.id = h.user_id 
    AND h.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    AND h.deleted = 0
WHERE u.deleted = 0
GROUP BY u.id
HAVING recent_usage > 0
ORDER BY recent_usage DESC, last_usage_time DESC;

-- 11. 验证数据完整性
SELECT '=== 数据完整性验证 ===' AS test_section;

-- 检查孤立的历史记录（没有对应用户）
SELECT 'Orphaned reply_history records:' as check_type, COUNT(*) as count
FROM reply_history h
LEFT JOIN users u ON h.user_id = u.id
WHERE u.id IS NULL;

-- 检查孤立的用户设置（没有对应用户）
SELECT 'Orphaned user_settings records:' as check_type, COUNT(*) as count
FROM user_settings s
LEFT JOIN users u ON s.user_id = u.id
WHERE u.id IS NULL;

-- 检查用户配额使用情况
SELECT 'Users exceeding daily quota:' as check_type, COUNT(*) as count
FROM users
WHERE today_used > daily_quota AND deleted = 0;

-- 12. 性能测试查询
SELECT '=== 性能测试 ===' AS test_section;

-- 测试索引效果 - 按用户ID和时间查询
EXPLAIN SELECT * FROM reply_history 
WHERE user_id = 1 AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY create_time DESC;

-- 测试索引效果 - 按情感类型查询
EXPLAIN SELECT * FROM reply_history 
WHERE emotion_result = '开心' AND deleted = 0
ORDER BY create_time DESC;

SELECT '=== 测试完成 ===' AS test_section;
