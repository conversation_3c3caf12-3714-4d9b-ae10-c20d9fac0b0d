package com.emotional.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.emotional.service.entity.ReplyHistory;
import com.emotional.service.mapper.ReplyHistoryMapper;
import com.emotional.service.service.ReplyHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 回复历史服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class ReplyHistoryServiceImpl extends ServiceImpl<ReplyHistoryMapper, ReplyHistory> 
    implements ReplyHistoryService {
    
    @Override
    public List<ReplyHistory> getHistoryByUserId(Long userId, int page, int size) {
        LambdaQueryWrapper<ReplyHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReplyHistory::getUserId, userId)
               .orderByDesc(ReplyHistory::getCreateTime)
               .last("LIMIT " + (page - 1) * size + ", " + size);
        
        return list(wrapper);
    }
    
    @Override
    public List<ReplyHistory> getFavoriteHistoryByUserId(Long userId, int page, int size) {
        LambdaQueryWrapper<ReplyHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReplyHistory::getUserId, userId)
               .eq(ReplyHistory::getIsFavorite, 1)
               .orderByDesc(ReplyHistory::getCreateTime)
               .last("LIMIT " + (page - 1) * size + ", " + size);
        
        return list(wrapper);
    }
    
    @Override
    public boolean toggleFavorite(Long historyId, Long userId) {
        try {
            // 先查询当前状态
            ReplyHistory history = getOne(new LambdaQueryWrapper<ReplyHistory>()
                .eq(ReplyHistory::getId, historyId)
                .eq(ReplyHistory::getUserId, userId));
            
            if (history == null) {
                log.warn("历史记录不存在或不属于当前用户: historyId={}, userId={}", historyId, userId);
                return false;
            }
            
            // 切换收藏状态
            int newFavoriteStatus = history.getIsFavorite() == 1 ? 0 : 1;
            
            LambdaUpdateWrapper<ReplyHistory> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ReplyHistory::getId, historyId)
                        .eq(ReplyHistory::getUserId, userId)
                        .set(ReplyHistory::getIsFavorite, newFavoriteStatus);
            
            boolean result = update(updateWrapper);
            
            if (result) {
                log.info("切换收藏状态成功: historyId={}, userId={}, newStatus={}", 
                        historyId, userId, newFavoriteStatus);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("切换收藏状态失败: historyId={}, userId={}", historyId, userId, e);
            return false;
        }
    }
    
    @Override
    public boolean deleteHistory(Long historyId, Long userId) {
        try {
            LambdaQueryWrapper<ReplyHistory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ReplyHistory::getId, historyId)
                   .eq(ReplyHistory::getUserId, userId);
            
            boolean result = remove(wrapper);
            
            if (result) {
                log.info("删除历史记录成功: historyId={}, userId={}", historyId, userId);
            } else {
                log.warn("删除历史记录失败，记录不存在或不属于当前用户: historyId={}, userId={}", 
                        historyId, userId);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("删除历史记录失败: historyId={}, userId={}", historyId, userId, e);
            return false;
        }
    }
    
    @Override
    public int getTodayUsageCount(Long userId) {
        LocalDateTime startOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        
        LambdaQueryWrapper<ReplyHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReplyHistory::getUserId, userId)
               .between(ReplyHistory::getCreateTime, startOfDay, endOfDay);
        
        return Math.toIntExact(count(wrapper));
    }
    
    @Override
    public int getTotalUsageCount(Long userId) {
        LambdaQueryWrapper<ReplyHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReplyHistory::getUserId, userId);
        
        return Math.toIntExact(count(wrapper));
    }
    
    @Override
    public boolean submitFeedback(Long historyId, Long userId, Integer rating, String feedback) {
        try {
            LambdaUpdateWrapper<ReplyHistory> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ReplyHistory::getId, historyId)
                        .eq(ReplyHistory::getUserId, userId)
                        .set(ReplyHistory::getUserRating, rating)
                        .set(ReplyHistory::getUserFeedback, feedback);
            
            boolean result = update(updateWrapper);
            
            if (result) {
                log.info("提交反馈成功: historyId={}, userId={}, rating={}", 
                        historyId, userId, rating);
            } else {
                log.warn("提交反馈失败，记录不存在或不属于当前用户: historyId={}, userId={}", 
                        historyId, userId);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("提交反馈失败: historyId={}, userId={}", historyId, userId, e);
            return false;
        }
    }
}
