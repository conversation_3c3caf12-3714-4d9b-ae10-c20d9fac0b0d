package com.emotional.service.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 激活码实体类
 * 
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("activation_codes")
public class ActivationCode {
    
    /**
     * 激活码ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 激活码
     */
    @TableField("code")
    private String code;
    
    /**
     * 激活码类型
     */
    @TableField("code_type")
    private String codeType;
    
    /**
     * 有效天数
     */
    @TableField("duration_days")
    private Integer durationDays;
    
    /**
     * 批次ID
     */
    @TableField("batch_id")
    private String batchId;
    
    /**
     * 创建者(管理员ID)
     */
    @TableField("created_by")
    private Long createdBy;
    
    /**
     * 使用者用户ID
     */
    @TableField("used_by")
    private Long usedBy;
    
    /**
     * 使用时间
     */
    @TableField("used_time")
    private LocalDateTime usedTime;
    
    /**
     * 激活码过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;
    
    /**
     * 状态：0-未使用，1-已使用，2-已过期
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}
