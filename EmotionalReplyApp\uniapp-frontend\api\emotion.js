/**
 * 情感分析相关 API
 */
import { post, get } from '../utils/request.js'

/**
 * 分析消息情感并生成回复
 * @param {string} message - 要分析的消息内容
 * @param {Array} replyStyles - 回复风格列表
 * @param {boolean} saveHistory - 是否保存历史记录
 * @returns {Promise} 分析结果
 */
export const analyzeEmotion = (message, replyStyles = [], saveHistory = true) => {
  return post('/emotion/analyze', {
    message,
    replyStyles,
    saveHistory
  })
}

/**
 * 批量分析消息情感
 * @param {Array} messages - 消息列表
 * @returns {Promise} 批量分析结果
 */
export const batchAnalyzeEmotion = (messages) => {
  return post('/emotion/batch-analyze', {
    messages
  })
}

/**
 * 获取情感分析历史
 * @param {object} params - 查询参数
 * @returns {Promise} 历史记录
 */
export const getEmotionHistory = (params = {}) => {
  return get('/emotion/history', {
    page: 1,
    size: 20,
    ...params
  })
}

/**
 * 获取情感分析统计
 * @param {string} timeRange - 时间范围 (day/week/month)
 * @returns {Promise} 统计数据
 */
export const getEmotionStatistics = (timeRange = 'week') => {
  return get('/emotion/statistics', {
    timeRange
  })
}

/**
 * 获取支持的回复风格
 * @returns {Promise} 回复风格列表
 */
export const getReplyStyles = () => {
  return get('/emotion/styles')
}

/**
 * 重新生成回复
 * @param {number} historyId - 历史记录ID
 * @param {Array} replyStyles - 新的回复风格
 * @returns {Promise} 新的回复结果
 */
export const regenerateReply = (historyId, replyStyles = []) => {
  return post(`/emotion/regenerate/${historyId}`, {
    replyStyles
  })
}

/**
 * 更新情感分析偏好设置
 * @param {object} preferences - 偏好设置
 * @returns {Promise} 更新结果
 */
export const updateEmotionPreferences = (preferences) => {
  return post('/emotion/preferences', preferences)
}

/**
 * 获取情感分析偏好设置
 * @returns {Promise} 偏好设置
 */
export const getEmotionPreferences = () => {
  return get('/emotion/preferences')
}

export default {
  analyzeEmotion,
  batchAnalyzeEmotion,
  getEmotionHistory,
  getEmotionStatistics,
  getReplyStyles,
  regenerateReply,
  updateEmotionPreferences,
  getEmotionPreferences
}
