<template>
  <view class="container">
    <view class="header">
      <text class="title">个人资料</text>
    </view>
    
    <view class="form-section">
      <view class="form-item">
        <text class="label">头像</text>
        <view class="avatar-container" @click="changeAvatar">
          <image :src="userInfo.avatar" class="avatar" />
          <text class="change-text">点击更换</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">昵称</text>
        <input 
          class="input" 
          v-model="userInfo.nickname" 
          placeholder="请输入昵称"
          @blur="updateNickname"
        />
      </view>
      
      <view class="form-item">
        <text class="label">用户名</text>
        <input 
          class="input readonly" 
          :value="userInfo.username" 
          readonly
          placeholder="用户名不可修改"
        />
      </view>
      
      <view class="form-item">
        <text class="label">邮箱</text>
        <input 
          class="input" 
          v-model="userInfo.email" 
          placeholder="请输入邮箱"
          @blur="updateEmail"
        />
      </view>
      
      <view class="form-item">
        <text class="label">手机号</text>
        <input 
          class="input" 
          v-model="userInfo.phone" 
          placeholder="请输入手机号"
          @blur="updatePhone"
        />
      </view>
      
      <view class="form-item">
        <text class="label">用户类型</text>
        <view class="user-type">
          <text class="type-text" :class="{ vip: userInfo.isVip }">
            {{ userInfo.isVip ? 'VIP用户' : '普通用户' }}
          </text>
          <button v-if="!userInfo.isVip" class="upgrade-btn" @click="upgradeVip">
            升级VIP
          </button>
        </view>
      </view>
    </view>
    
    <view class="stats-section">
      <text class="section-title">使用统计</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.todayUsage || 0 }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.totalUsage || 0 }}</text>
          <text class="stat-label">总计使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.dailyQuota || 10 }}</text>
          <text class="stat-label">每日配额</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ remainingQuota }}</text>
          <text class="stat-label">剩余配额</text>
        </view>
      </view>
    </view>
    
    <view class="actions">
      <button class="save-btn" @click="saveProfile">保存修改</button>
    </view>
  </view>
</template>

<script>
import { UserManager } from '../../utils/user.js'

export default {
  name: 'UserProfile',
  
  data() {
    return {
      userInfo: {}
    }
  },
  
  computed: {
    remainingQuota() {
      return Math.max(0, (this.userInfo.dailyQuota || 10) - (this.userInfo.todayUsage || 0))
    }
  },
  
  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    }
  },

  onLoad() {
    // 检查登录状态
    if (!this.isLoggedIn) {
      uni.showModal({
        title: '需要登录',
        content: '请先登录以查看个人资料',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
      return
    }
    this.loadUserInfo()
  },
  
  methods: {
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = { ...UserManager.getUserInfo() } || UserManager.getDefaultUserInfo()
    },
    
    // 更换头像
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0]
        }
      })
    },
    
    // 更新昵称
    updateNickname() {
      if (this.userInfo.nickname && this.userInfo.nickname.trim()) {
        UserManager.updateUserField('nickname', this.userInfo.nickname.trim())
      }
    },
    
    // 更新邮箱
    updateEmail() {
      if (this.userInfo.email) {
        UserManager.updateUserField('email', this.userInfo.email)
      }
    },
    
    // 更新手机号
    updatePhone() {
      if (this.userInfo.phone) {
        UserManager.updateUserField('phone', this.userInfo.phone)
      }
    },
    
    // 升级VIP
    upgradeVip() {
      uni.showModal({
        title: '升级VIP',
        content: '是否升级为VIP用户？（演示功能）',
        success: (res) => {
          if (res.confirm) {
            this.userInfo.isVip = true
            this.userInfo.dailyQuota = 50
            UserManager.updateUserField('isVip', true)
            UserManager.updateUserField('dailyQuota', 50)
            uni.showToast({
              title: 'VIP升级成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 保存资料
    saveProfile() {
      try {
        UserManager.setUserInfo(this.userInfo)
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  padding: 40rpx 30rpx;
  text-align: center;
  
  .title {
    color: white;
    font-size: 36rpx;
    font-weight: bold;
  }
}

.form-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .form-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      width: 150rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .input {
      flex: 1;
      height: 60rpx;
      padding: 0 20rpx;
      border: 1rpx solid #e0e0e0;
      border-radius: 8rpx;
      font-size: 28rpx;
      
      &.readonly {
        background-color: #f5f5f5;
        color: #999;
      }
    }
    
    .avatar-container {
      display: flex;
      align-items: center;
      
      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      
      .change-text {
        color: #2196F3;
        font-size: 26rpx;
      }
    }
    
    .user-type {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .type-text {
        font-size: 28rpx;
        color: #666;
        
        &.vip {
          color: #ff6b35;
          font-weight: bold;
        }
      }
      
      .upgrade-btn {
        background: #ff6b35;
        color: white;
        border: none;
        border-radius: 20rpx;
        padding: 10rpx 20rpx;
        font-size: 24rpx;
      }
    }
  }
}

.stats-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .section-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 48rpx;
        font-weight: bold;
        color: #2196F3;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.actions {
  padding: 40rpx 30rpx;
  
  .save-btn {
    width: 100%;
    height: 88rpx;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
  }
}
</style>
