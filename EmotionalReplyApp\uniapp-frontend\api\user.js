/**
 * 用户相关 API
 */
import { post, get, put, upload } from '../utils/request.js'

/**
 * 用户注册
 * @param {object} registerData - 注册数据
 * @returns {Promise} 注册结果
 */
export const register = (registerData) => {
  return post('/user/register', registerData)
}

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise} 登录结果
 */
export const login = (username, password) => {
  return post('/user/login', {
    username,
    password,
    rememberMe: false
  })
}

/**
 * 获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise} 用户信息
 */
export const getUserInfo = (userId) => {
  return get(`/user/info/${userId}`)
}

/**
 * 更新用户信息
 * @param {number} userId - 用户ID
 * @param {object} userInfo - 用户信息
 * @returns {Promise} 更新结果
 */
export const updateUserInfo = (userId, userInfo) => {
  return put(`/user/info/${userId}`, userInfo)
}

/**
 * 修改密码
 * @param {number} userId - 用户ID
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 * @returns {Promise} 修改结果
 */
export const changePassword = (userId, oldPassword, newPassword) => {
  return put(`/user/password/${userId}`, {
    oldPassword,
    newPassword
  })
}

/**
 * 上传头像
 * @param {number} userId - 用户ID
 * @param {string} filePath - 文件路径
 * @returns {Promise} 上传结果
 */
export const uploadAvatar = (userId, filePath) => {
  return upload(`/user/avatar/${userId}`, filePath)
}

/**
 * 验证用户是否存在
 * @param {string} identifier - 用户标识（手机号或邮箱）
 * @returns {Promise} 验证结果
 */
export const checkUserExists = (identifier) => {
  return post('/user/check-exists', {
    identifier
  })
}

/**
 * 发送注册验证码
 * @param {string} email - 邮箱地址
 * @returns {Promise} 发送结果
 */
export const sendRegisterCode = (email) => {
  return post('/user/send-register-code', {
    email
  })
}

/**
 * 发送忘记密码验证码
 * @param {string} identifier - 用户标识（手机号或邮箱）
 * @returns {Promise} 发送结果
 */
export const sendResetPasswordCode = (identifier) => {
  return post('/user/send-reset-code', {
    identifier
  })
}

/**
 * 验证注册验证码
 * @param {string} email - 邮箱地址
 * @param {string} code - 验证码
 * @returns {Promise} 验证结果
 */
export const verifyRegisterCode = (email, code) => {
  return post('/user/verify-register-code', {
    email,
    code
  })
}

/**
 * 验证重置密码验证码
 * @param {string} identifier - 用户标识
 * @param {string} code - 验证码
 * @returns {Promise} 验证结果
 */
export const verifyResetCode = (identifier, code) => {
  return post('/user/verify-reset-code', {
    identifier,
    code
  })
}

/**
 * 重置密码
 * @param {string} identifier - 用户标识
 * @param {string} code - 验证码
 * @param {string} newPassword - 新密码
 * @returns {Promise} 重置结果
 */
export const resetPassword = (identifier, code, newPassword) => {
  return post('/user/reset-password', {
    identifier,
    code,
    newPassword
  })
}

export default {
  register,
  login,
  getUserInfo,
  updateUserInfo,
  changePassword,
  uploadAvatar,
  checkUserExists,
  sendRegisterCode,
  verifyRegisterCode,
  sendResetPasswordCode,
  verifyResetCode,
  resetPassword
}
