<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-section">
      <view class="user-info">
        <image :src="avatarSrc" class="avatar" @click="changeAvatar" @error="onAvatarError" mode="aspectFit" />
        <view class="user-details">
          <text class="nickname">{{ userInfo.nickname }}</text>
          <text class="user-type">{{ getUserRoleText() }}</text>
        </view>
      </view>
      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.todayUsage }}</text>
          <text class="stat-label">今日使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.totalUsage }}</text>
          <text class="stat-label">总计使用</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.remainingQuota }}</text>
          <text class="stat-label">剩余配额</text>
        </view>
      </view>
    </view>
    
    <!-- 设置选项 -->
    <view class="settings-section">
      <text class="section-title">应用设置</text>
      
      <view class="setting-item" @click="toggleFloatingBubble">
        <text class="setting-label">悬浮气泡</text>
        <switch :checked="settings.showFloatingBubble" @change="onFloatingBubbleChange" />
      </view>
      
      <view class="setting-item" @click="toggleAutoAnalysis">
        <text class="setting-label">自动分析</text>
        <switch :checked="settings.autoAnalysis" @change="onAutoAnalysisChange" />
      </view>
      
      <view class="setting-item" @click="selectDefaultStyle">
        <text class="setting-label">默认回复风格</text>
        <text class="setting-value">{{ getStyleName(settings.defaultStyle) }}</text>
      </view>
      
      <view class="setting-item" @click="selectTheme">
        <text class="setting-label">主题模式</text>
        <text class="setting-value">{{ getThemeName(settings.theme) }}</text>
      </view>
    </view>
    
    <!-- 账户管理 -->
    <view class="account-section">
      <text class="section-title">账户管理</text>
      
      <!-- 管理员显示生成激活码 -->
      <view class="setting-item" @click="generateActivationCode" v-if="userInfo.isAdmin">
        <text class="setting-label">生成激活码</text>
        <text class="setting-value premium">管理功能</text>
      </view>

      <!-- 普通用户显示升级VIP -->
      <view class="setting-item" @click="upgradeVip" v-if="!userInfo.isVip && !userInfo.isAdmin">
        <text class="setting-label">升级VIP</text>
        <text class="setting-value premium">立即升级</text>
      </view>
      
      <view class="setting-item" @click="viewProfile">
        <text class="setting-label">个人资料</text>
        <text class="setting-value">></text>
      </view>
      
      <view class="setting-item" @click="changePassword">
        <text class="setting-label">修改密码</text>
        <text class="setting-value">></text>
      </view>
    </view>
    
    <!-- 其他设置 -->
    <view class="other-section">
      <text class="section-title">其他</text>
      
      <view class="setting-item" @click="clearCache">
        <text class="setting-label">清除缓存</text>
        <text class="setting-value">{{ cacheSize }}</text>
      </view>
      
      <view class="setting-item" @click="checkUpdate">
        <text class="setting-label">检查更新</text>
        <text class="setting-value">v1.0.0</text>
      </view>
      
      <view class="setting-item" @click="showAbout">
        <text class="setting-label">关于我们</text>
        <text class="setting-value">></text>
      </view>
      
      <view class="setting-item" @click="feedback">
        <text class="setting-label">意见反馈</text>
        <text class="setting-value">></text>
      </view>
    </view>
    
    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">
        <text>退出登录</text>
      </button>
    </view>
  </view>
</template>

<script>
import { getReplyStyles } from '../../api/emotion.js'
import { getUserStats } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'

export default {
  name: 'SettingsPage',
  
  data() {
    return {
      userInfo: UserManager.getUserInfo() || {
        nickname: '未登录',
        avatar: 'static/images/default-avatar.png',
        isVip: false,
        isAdmin: false,
        todayUsage: 0,
        totalUsage: 0,
        remainingQuota: 0
      },
      settings: {
        showFloatingBubble: true,
        autoAnalysis: false,
        defaultStyle: 'warm_caring',
        theme: 'auto'
      },
      cacheSize: '12.5MB',
      replyStyles: {} // 从API获取的回复风格
    }
  },
  
  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    },

    // 头像路径处理
    avatarSrc() {
      if (!this.userInfo.avatar) {
        return '/static/images/default-avatar.png'
      }

      // 如果是网络图片或base64，直接返回
      if (this.userInfo.avatar.startsWith('http') || this.userInfo.avatar.startsWith('data:')) {
        return this.userInfo.avatar
      }

      // 如果是本地路径，确保以 / 开头
      if (this.userInfo.avatar.startsWith('static/')) {
        return '/' + this.userInfo.avatar
      }

      return this.userInfo.avatar
    }
  },

  onLoad() {
    // 路由守卫检查
    if (!AuthGuard.pageGuard('pages/settings/settings')) {
      return
    }

    this.loadUserInfo()
    this.loadSettings()
    this.loadReplyStyles()
  },
  
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        // 确保用户信息是最新的
        this.userInfo = UserManager.getUserInfo() || UserManager.getDefaultUserInfo()

        // 尝试从API获取用户统计
        const currentUserId = UserManager.getCurrentUserId()
        const stats = await getUserStats(currentUserId)
        this.userInfo = {
          ...this.userInfo,
          todayUsage: stats.todayUsage || 0,
          totalUsage: stats.totalUsage || 0,
          remainingQuota: stats.remainingQuota || 10
        }
      } catch (error) {
        console.log('获取用户信息失败，使用本地数据:', error)
        this.userInfo = UserManager.getUserInfo() || UserManager.getDefaultUserInfo()
      }
    },
    
    // 加载设置
    loadSettings() {
      const savedSettings = uni.getStorageSync('settings')
      if (savedSettings) {
        this.settings = { ...this.settings, ...savedSettings }
      }
    },

    // 加载回复风格
    async loadReplyStyles() {
      try {
        this.replyStyles = await getReplyStyles()
        console.log('回复风格加载成功:', this.replyStyles)
      } catch (error) {
        console.log('获取回复风格失败，使用默认值:', error)
        // 使用默认风格
        this.replyStyles = {
          warm_caring: '温暖关怀',
          humorous: '幽默风趣',
          rational: '理性分析',
          concise: '简洁直接',
          romantic: '浪漫情话'
        }
      }
    },
    
    // 头像加载错误处理
    onAvatarError(e) {
      console.log('头像加载失败，使用备用方案', e)
      // 使用备用头像
      this.userInfo.avatar = '/static/images/default-avatar.png'

      // 如果默认头像也加载失败，使用SVG备用方案
      setTimeout(() => {
        if (this.userInfo.avatar === '/static/images/default-avatar.png') {
          this.userInfo.avatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiByeD0iNjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB4PSIzMCIgeT0iMzAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMTIgMTJDMTQuNzYxNCAxMiAxNyA5Ljc2MTQyIDE3IDdDMTcgNC4yMzg1OCAxNC43NjE0IDIgMTIgMkM5LjIzODU4IDIgNyA0LjIzODU4IDcgN0M3IDkuNzYxNDIgOS4yMzg1OCAxMiAxMiAxMloiIGZpbGw9IiM5Q0E0QUYiLz4KPHN0cm9rZSBkPSJNMTIgMTJDMTQuNzYxNCAxMiAxNyA5Ljc2MTQyIDE3IDdDMTcgNC4yMzg1OCAxNC43NjE0IDIgMTIgMkM5LjIzODU4IDIgNyA0LjIzODU4IDcgN0M3IDkuNzYxNDIgOS4yMzg1OCAxMiAxMiAxMloiIHN0cm9rZT0iIzlDQTRBRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHN0cm9rZSBkPSJNMjEgMjFWMTlDMjEgMTYuNzkwOSAxOS4yMDkxIDE1IDE3IDE1SDdDNC43OTA4NiAxNSAzIDE2Ljc5MDkgMyAxOVYyMSIgc3Ryb2tlPSIjOUNBNEFGIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+'
        }
      }, 100)
    },

    // 更换头像
    changeAvatar() {
      if (!this.isLoggedIn) {
        this.showLoginRequired()
        return
      }
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0]
          // 更新本地用户信息
          UserManager.updateUserField('avatar', res.tempFilePaths[0])
          // TODO: 上传头像到服务器
          uni.showToast({
            title: '头像已更新',
            icon: 'success'
          })
        }
      })
    },
    
    // 悬浮气泡开关
    onFloatingBubbleChange(e) {
      this.settings.showFloatingBubble = e.detail.value
      this.saveSettings()
    },
    
    // 自动分析开关
    onAutoAnalysisChange(e) {
      this.settings.autoAnalysis = e.detail.value
      this.saveSettings()
    },
    
    // 选择默认风格
    selectDefaultStyle() {
      // 使用从API获取的风格列表
      const styles = Object.keys(this.replyStyles).map(key => ({
        value: key,
        label: this.replyStyles[key]
      }))

      uni.showActionSheet({
        itemList: styles.map(s => s.label),
        success: (res) => {
          this.settings.defaultStyle = styles[res.tapIndex].value
          this.saveSettings()
        }
      })
    },
    
    // 选择主题
    selectTheme() {
      const themes = [
        { value: 'auto', label: '跟随系统' },
        { value: 'light', label: '浅色模式' },
        { value: 'dark', label: '深色模式' }
      ]
      
      uni.showActionSheet({
        itemList: themes.map(t => t.label),
        success: (res) => {
          this.settings.theme = themes[res.tapIndex].value
          this.saveSettings()
        }
      })
    },
    
    // 获取用户角色文本
    getUserRoleText() {
      if (!this.userInfo) {
        return '未登录'
      }

      // 调试信息（可选）
      // console.log('用户信息调试:', {
      //   isAdmin: this.userInfo.isAdmin,
      //   isVip: this.userInfo.isVip,
      //   userInfo: this.userInfo
      // })

      // 按优先级检查：管理员 > VIP用户 > 普通用户
      if (this.userInfo.isAdmin === 1 || this.userInfo.isAdmin === true) {
        return '管理员'
      } else if (this.userInfo.isVip === 1 || this.userInfo.isVip === true) {
        return 'VIP用户'
      } else {
        return '普通用户'
      }
    },

    // 生成激活码（管理员功能）
    generateActivationCode() {
      if (!this.userInfo.isAdmin) {
        uni.showToast({
          title: '权限不足',
          icon: 'error'
        })
        return
      }

      // 显示激活码类型选择
      uni.showActionSheet({
        itemList: [
          '30天VIP激活码',
          '90天VIP激活码',
          '180天VIP激活码',
          '365天VIP激活码'
        ],
        success: (res) => {
          const codeTypes = [
            { type: 'premium_1m', days: 30, name: '30天VIP' },
            { type: 'premium_3m', days: 90, name: '90天VIP' },
            { type: 'premium_6m', days: 180, name: '180天VIP' },
            { type: 'premium_1y', days: 365, name: '365天VIP' }
          ]

          const selectedType = codeTypes[res.tapIndex]
          this.doGenerateActivationCode(selectedType)
        }
      })
    },

    // 执行生成激活码
    async doGenerateActivationCode(codeTypeInfo) {
      try {
        uni.showLoading({
          title: '生成中...'
        })

        // 调用后端API生成激活码
        const { generateActivationCode } = await import('../../api/activation.js')
        const response = await generateActivationCode(
          codeTypeInfo.type,
          codeTypeInfo.days,
          this.userInfo.id,
          `管理员${this.userInfo.nickname}生成的${codeTypeInfo.name}激活码`
        )

        uni.hideLoading()

        console.log('API响应:', response)

        if (response && response.success && response.data && response.data.code) {
          // 显示生成的激活码
          uni.showModal({
            title: '激活码生成成功',
            content: `激活码: ${response.data.code}\n类型: ${codeTypeInfo.name}\n有效期: ${codeTypeInfo.days}天\n激活码有效期: 30天\n\n请妥善保管此激活码`,
            showCancel: true,
            cancelText: '关闭',
            confirmText: '复制',
            success: (res) => {
              if (res.confirm) {
                // 复制到剪贴板
                uni.setClipboardData({
                  data: response.data.code,
                  success: () => {
                    uni.showToast({
                      title: '已复制到剪贴板',
                      icon: 'success'
                    })
                  }
                })
              }
            }
          })
        } else {
          throw new Error(response.message || '生成激活码失败')
        }

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '生成失败',
          icon: 'error'
        })
        console.error('生成激活码失败:', error)
      }
    },

    // 生成随机激活码
    generateRandomCode() {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
      let result = ''

      // 生成格式：XXXX-XXXX-XXXX-XXXX
      for (let i = 0; i < 4; i++) {
        if (i > 0) result += '-'
        for (let j = 0; j < 4; j++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length))
        }
      }

      return result
    },

    // 升级VIP
    upgradeVip() {
      uni.showActionSheet({
        itemList: [
          '激活码兑换',
          '购买VIP套餐'
        ],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 激活码兑换
            uni.navigateTo({
              url: '/pages/activation/activation'
            })
          } else if (res.tapIndex === 1) {
            // 购买VIP套餐
            uni.navigateTo({
              url: '/pages/vip/upgrade'
            })
          }
        }
      })
    },
    
    // 查看个人资料
    viewProfile() {
      if (!this.isLoggedIn) {
        uni.showModal({
          title: '需要登录',
          content: '请先登录以查看个人资料',
          showCancel: false,
          success: () => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        })
        return
      }
      uni.navigateTo({
        url: '/pages/user/profile'
      })
    },
    
    // 修改密码
    changePassword() {
      uni.navigateTo({
        url: '/pages/user/change-password'
      })
    },
    
    // 清除缓存
    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除缓存逻辑
            this.cacheSize = '0MB'
            uni.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 检查更新
    checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '已是最新版本',
          icon: 'success'
        })
      }, 2000)
    },
    
    // 关于我们
    showAbout() {
      uni.navigateTo({
        url: '/pages/about/about'
      })
    },
    
    // 意见反馈
    feedback() {
      uni.navigateTo({
        url: '/pages/feedback/feedback'
      })
    },
    
    // 退出登录
    logout() {
      if (!this.isLoggedIn) {
        uni.navigateTo({
          url: '/pages/login/login'
        })
        return
      }

      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 使用路由守卫的退出登录方法
            AuthGuard.logoutRedirect()

            // 清除用户数据
            UserManager.clearUserInfo()

            uni.showToast({
              title: '已退出登录',
              icon: 'success'
            })
          }
        }
      })
    },

    // 显示登录提示
    showLoginRequired() {
      uni.showModal({
        title: '需要登录',
        content: '请先登录以使用此功能',
        showCancel: false,
        success: () => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
      })
    },
    
    // 保存设置
    saveSettings() {
      uni.setStorageSync('settings', this.settings)
    },
    
    // 获取风格名称
    getStyleName(style) {
      return this.replyStyles[style] || style
    },
    
    // 获取主题名称
    getThemeName(theme) {
      const themeMap = {
        auto: '跟随系统',
        light: '浅色模式',
        dark: '深色模式'
      }
      return themeMap[theme] || theme
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.user-section {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  
  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 30rpx;
      border: 4rpx solid rgba(255, 255, 255, 0.3);
      background-color: #f5f5f5;
      display: block;
      object-fit: cover;
    }
    
    .user-details {
      flex: 1;
      
      .nickname {
        display: block;
        color: white;
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .user-type {
        color: rgba(255, 255, 255, 0.8);
        font-size: 26rpx;
      }
    }
  }
  
  .user-stats {
    display: flex;
    justify-content: space-around;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        display: block;
        color: white;
        font-size: 48rpx;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .stat-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 24rpx;
      }
    }
  }
}

.settings-section, .account-section, .other-section {
  background: white;
  margin-bottom: 20rpx;
  
  .section-title {
    display: block;
    padding: 30rpx 30rpx 20rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
  
  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .setting-label {
      font-size: 28rpx;
      color: #333;
    }
    
    .setting-value {
      font-size: 26rpx;
      color: #666;
      
      &.premium {
        color: #ff6b35;
        font-weight: bold;
      }
    }
  }
}

.logout-section {
  padding: 40rpx 30rpx;
  
  .logout-btn {
    width: 100%;
    height: 88rpx;
    background: #ff4757;
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}
</style>
