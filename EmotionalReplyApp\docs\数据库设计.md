# 情感回复助手 - 数据库设计文档

## 数据库概述

### 技术选型
- **数据库**: MySQL 8.0+
- **连接池**: HikariCP
- **ORM框架**: MyBatis Plus
- **字符集**: utf8mb4 (支持emoji表情)
- **存储引擎**: InnoDB

### 设计原则
- **数据一致性**: 使用外键约束保证数据完整性
- **性能优化**: 合理设计索引，支持高并发查询
- **扩展性**: 预留字段，支持功能扩展
- **数据安全**: 敏感信息加密存储

## 数据表设计

### 1. 用户表 (users)
存储用户基本信息、角色权限和会员信息

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键ID |
| user_id | VARCHAR | 64 | UNIQUE, NOT NULL | 用户唯一标识 |
| username | VARCHAR | 50 | UNIQUE, NOT NULL | 用户名 |
| email | VARCHAR | 100 | UNIQUE | 邮箱地址 |
| password_hash | VARCHAR | 255 | NOT NULL | 密码哈希 |
| nickname | VARCHAR | 50 | - | 用户昵称 |
| avatar_url | VARCHAR | 255 | - | 头像URL |
| role | VARCHAR | 20 | DEFAULT 'free' | 用户角色(free:免费用户,premium:高级会员,admin:管理员) |
| premium_expire | DATETIME | - | - | 高级会员到期时间 |
| daily_quota | INT | - | DEFAULT 10 | 每日使用配额 |
| used_today | INT | - | DEFAULT 0 | 今日已使用次数 |
| total_used | BIGINT | - | DEFAULT 0 | 总使用次数 |
| last_used_date | DATE | - | - | 最后使用日期 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| status | TINYINT | - | DEFAULT 1 | 状态(0:禁用,1:正常,2:冻结) |

### 2. 用户偏好设置表 (user_preferences)
存储用户个性化设置

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键ID |
| user_id | VARCHAR | 64 | UNIQUE, NOT NULL, FK | 用户ID |
| preferred_styles | JSON | - | - | 偏好的回复风格 |
| reply_length_preference | TINYINT | - | DEFAULT 2 | 回复长度偏好(1:简短,2:中等,3:详细) |
| emotion_sensitivity | DECIMAL | 3,2 | DEFAULT 0.5 | 情感敏感度 |
| auto_copy | TINYINT | - | DEFAULT 1 | 是否自动复制 |
| bubble_position | JSON | - | - | 气泡位置偏好 |
| theme_color | VARCHAR | 10 | DEFAULT '#2196F3' | 主题色 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 3. 激活码表 (activation_codes)
存储管理员生成的激活码信息

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键ID |
| code | VARCHAR | 32 | UNIQUE, NOT NULL | 激活码 |
| code_type | VARCHAR | 20 | NOT NULL | 激活码类型(premium_1m:1个月高级会员,premium_3m:3个月,premium_1y:1年) |
| duration_days | INT | - | NOT NULL | 有效天数 |
| batch_id | VARCHAR | 64 | - | 批次ID(便于批量管理) |
| created_by | VARCHAR | 64 | NOT NULL | 创建者(管理员ID) |
| used_by | VARCHAR | 64 | - | 使用者用户ID |
| used_time | DATETIME | - | - | 使用时间 |
| expire_time | DATETIME | - | NOT NULL | 激活码过期时间 |
| status | TINYINT | - | DEFAULT 0 | 状态(0:未使用,1:已使用,2:已过期) |
| remark | VARCHAR | 255 | - | 备注信息 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计:**
- `uk_code (code)` - 激活码唯一约束
- `idx_batch_id (batch_id)` - 批次查询优化
- `idx_created_by (created_by)` - 创建者查询优化
- `idx_status_expire (status, expire_time)` - 状态和过期时间复合索引

### 4. 使用统计表 (usage_statistics)
存储用户使用统计和配额管理

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | 主键ID |
| user_id | VARCHAR | 64 | NOT NULL | 用户ID |
| date | DATE | - | NOT NULL | 统计日期 |
| daily_used_count | INT | - | DEFAULT 0 | 当日使用次数 |
| daily_quota | INT | - | DEFAULT 10 | 当日配额 |
| quota_exceeded_count | INT | - | DEFAULT 0 | 超额尝试次数 |
| most_used_style | VARCHAR | 20 | - | 最常用风格 |
| avg_response_time | INT | - | DEFAULT 0 | 平均响应时间(毫秒) |
| success_rate | DECIMAL | 5,2 | DEFAULT 100.00 | 成功率 |
| created_time | DATETIME | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计:**
- `uk_user_date (user_id, date)` - 唯一约束
- `idx_date (date)` - 日期查询优化

## 数据字典

### 用户角色 (role)
- `free` - 免费用户 (每日10次)
- `premium` - 高级会员 (每日150次)
- `admin` - 管理员 (无限制)

### 激活码类型 (code_type)
- `premium_1m` - 1个月高级会员
- `premium_3m` - 3个月高级会员
- `premium_6m` - 6个月高级会员
- `premium_1y` - 1年高级会员

### 激活码状态 (activation_codes.status)
- `0` - 未使用
- `1` - 已使用
- `2` - 已过期

### 用户状态 (status)
- `0` - 禁用
- `1` - 正常
- `2` - 冻结

### 回复风格 (reply_style)
- `warm_caring` - 温暖关怀型
- `humorous` - 幽默风趣型
- `rational` - 理性分析型
- `concise` - 简洁直接型
- `romantic` - 浪漫情话型

### 配额规则
| 角色类型 | 每日配额 | 月费 | 超额处理 |
|----------|----------|------|----------|
| free | 10次 | 免费 | 提示升级 |
| premium | 150次 | ¥9.9 | 软限制提醒 |
| admin | 无限制 | - | - |

## 性能优化

### 索引策略
1. **主键索引**: 所有表都有自增主键
2. **唯一索引**: 用户ID等唯一字段
3. **复合索引**: 多字段查询优化
4. **时间索引**: 支持时间范围查询

### 分区策略
```sql
-- 按月分区消息记录表
ALTER TABLE message_records PARTITION BY RANGE (YEAR(created_time)*100 + MONTH(created_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- 继续添加分区...
);
```

### 数据清理策略
```sql
-- 定期清理90天前的消息记录
DELETE FROM message_records 
WHERE created_time < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 定期清理未被选择的回复记录
DELETE FROM reply_records 
WHERE is_selected = 0 AND created_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## 备份和恢复

### 备份策略
- **全量备份**: 每日凌晨进行全量备份
- **增量备份**: 每小时进行增量备份
- **binlog备份**: 实时备份binlog文件

### 恢复策略
- **时间点恢复**: 基于binlog进行时间点恢复
- **表级恢复**: 针对特定表的数据恢复
- **灾难恢复**: 主从切换和数据中心切换
