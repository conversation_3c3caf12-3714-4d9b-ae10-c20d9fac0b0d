<template>
  <view class="container">
    <view class="header">
      <text class="title">消息输入</text>
      <text class="subtitle">输入收到的消息，获取回复建议</text>
    </view>
    
    <view class="input-section">
      <view class="input-label">
        <text>收到的消息</text>
      </view>
      <textarea 
        v-model="inputMessage"
        class="message-input"
        placeholder="请输入收到的消息内容..."
        :maxlength="500"
        auto-height
      />
      <view class="char-count">
        <text>{{ inputMessage.length }}/500</text>
      </view>
    </view>
    
    <view class="action-section">
      <button 
        class="analyze-btn"
        :disabled="!inputMessage.trim()"
        @click="analyzeMessage"
      >
        <text>分析并生成回复</text>
      </button>
    </view>
    
    <view class="quick-actions" v-if="recentMessages.length > 0">
      <text class="section-title">最近消息</text>
      <view class="recent-list">
        <view 
          v-for="(msg, index) in recentMessages" 
          :key="index"
          class="recent-item"
          @click="selectRecentMessage(msg)"
        >
          <text class="recent-text">{{ msg }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { HistoryManager } from '../../utils/storage.js'
import { getHistoryList } from '../../api/history.js'
import { UserManager } from '../../utils/user.js'
import { AuthGuard } from '../../utils/auth-guard.js'

export default {
  name: 'MessageInput',
  
  data() {
    return {
      inputMessage: '',
      recentMessages: []
    }
  },

  computed: {
    // 是否已登录
    isLoggedIn() {
      return UserManager.isLoggedIn()
    }
  },

  onLoad() {
    // 路由守卫检查
    if (!AuthGuard.pageGuard('pages/message/input')) {
      return
    }

    this.loadRecentMessages()
  },
  
  methods: {
    // 加载最近消息
    async loadRecentMessages() {
      try {
        // 尝试从API获取最近的历史记录
        const currentUserId = UserManager.getCurrentUserId()
        const historyList = await getHistoryList(1, 10, currentUserId)

        // 提取原始消息作为最近消息
        this.recentMessages = historyList
          .map(item => item.originalMessage)
          .filter((message, index, arr) => arr.indexOf(message) === index) // 去重
          .slice(0, 5) // 只取前5条

      } catch (error) {
        console.log('API获取失败，使用本地历史记录:', error)

        // API失败时从本地存储获取
        const localHistory = HistoryManager.getHistory()
        this.recentMessages = localHistory
          .map(item => item.originalMessage)
          .filter((message, index, arr) => arr.indexOf(message) === index) // 去重
          .slice(0, 5) // 只取前5条

        // 如果本地也没有，使用默认消息
        if (this.recentMessages.length === 0) {
          this.recentMessages = [
            '今天心情不太好',
            '工作压力好大啊',
            '想你了',
            '晚上一起吃饭吗？',
            '谢谢你的帮助'
          ]
        }
      }
    },

    // 分析消息
    analyzeMessage() {
      if (!this.isLoggedIn) {
        uni.showModal({
          title: '需要登录',
          content: '请先登录以使用智能回复功能',
          showCancel: false,
          success: () => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }
        })
        return
      }

      if (!this.inputMessage.trim()) {
        uni.showToast({
          title: '请输入消息内容',
          icon: 'none'
        })
        return
      }

      // 跳转到回复生成页面
      uni.navigateTo({
        url: `/pages/reply/generation?message=${encodeURIComponent(this.inputMessage)}`
      })
    },
    
    // 选择最近消息
    selectRecentMessage(message) {
      this.inputMessage = message
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
  }
}

.input-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .input-label {
    margin-bottom: 20rpx;
    
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .message-input {
    width: 100%;
    min-height: 200rpx;
    padding: 20rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 12rpx;
    font-size: 28rpx;
    line-height: 1.5;
    background-color: #fafafa;
  }
  
  .char-count {
    text-align: right;
    margin-top: 16rpx;
    
    text {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.action-section {
  margin-bottom: 40rpx;
  
  .analyze-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #2196F3, #21CBF3);
    border-radius: 44rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      color: white;
      font-size: 32rpx;
      font-weight: bold;
    }
    
    &:disabled {
      background: #ccc;
    }
  }
}

.quick-actions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .recent-list {
    .recent-item {
      padding: 20rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .recent-text {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}
</style>
