package com.emotional.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.emotional.service.dto.request.UserLoginRequest;
import com.emotional.service.dto.request.UserRegisterRequest;
import com.emotional.service.dto.response.UserLoginResponse;
import com.emotional.service.entity.User;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface UserService extends IService<User> {
    
    /**
     * 用户登录
     * 
     * @param request 登录请求
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 登录响应
     */
    UserLoginResponse login(UserLoginRequest request, String clientIp, String userAgent);

    /**
     * 用户注册
     *
     * @param request 注册请求
     * @return 用户信息
     */
    User register(UserRegisterRequest request);

    /**
     * 根据ID获取用户
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    User getUserById(Long userId);
    
    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User getUserByUsername(String username);

    /**
     * 根据邮箱获取用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    User getUserByEmail(String email);
    
    /**
     * 更新用户信息
     * 
     * @param userId 用户ID
     * @param userInfo 用户信息
     * @return 是否成功
     */
    boolean updateUserInfo(Long userId, User userInfo);
    
    /**
     * 修改密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密密码
     * @return 是否匹配
     */
    boolean verifyPassword(String rawPassword, String encodedPassword);
    
    /**
     * 加密密码
     * 
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    String encodePassword(String rawPassword);
    
    /**
     * 生成JWT令牌
     * 
     * @param user 用户信息
     * @return JWT令牌
     */
    String generateToken(User user);
    
    /**
     * 更新用户登录信息
     *
     * @param userId 用户ID
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     */
    void updateLoginInfo(Long userId, String clientIp, String userAgent);

    /**
     * 检查用户是否存在
     *
     * @param identifier 用户标识（手机号或邮箱）
     * @return 是否存在
     */
    boolean checkUserExists(String identifier);

    /**
     * 发送注册验证码
     *
     * @param email 邮箱地址
     * @return 是否发送成功
     */
    boolean sendRegisterCode(String email);

    /**
     * 验证注册验证码
     *
     * @param email 邮箱地址
     * @param code 验证码
     * @return 是否验证成功
     */
    boolean verifyRegisterCode(String email, String code);

    /**
     * 发送重置密码验证码
     *
     * @param identifier 用户标识（手机号或邮箱）
     * @return 是否发送成功
     */
    boolean sendResetPasswordCode(String identifier);

    /**
     * 验证重置密码验证码
     *
     * @param identifier 用户标识
     * @param code 验证码
     * @return 是否验证成功
     */
    boolean verifyResetCode(String identifier, String code);

    /**
     * 重置密码
     *
     * @param identifier 用户标识
     * @param code 验证码
     * @param newPassword 新密码
     * @return 是否重置成功
     */
    boolean resetPassword(String identifier, String code, String newPassword);
}
