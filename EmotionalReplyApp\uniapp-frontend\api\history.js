/**
 * 历史记录相关 API
 */
import { post, get, del } from '../utils/request.js'

/**
 * 获取历史记录列表
 * @param {number} page - 页码
 * @param {number} size - 每页大小
 * @param {number} userId - 用户ID
 * @returns {Promise} 历史记录列表
 */
export const getHistoryList = (page = 1, size = 20, userId = 1) => {
  return get('/history/list', {
    page,
    size,
    userId
  })
}

/**
 * 获取收藏的历史记录
 * @param {number} page - 页码
 * @param {number} size - 每页大小
 * @param {number} userId - 用户ID
 * @returns {Promise} 收藏的历史记录列表
 */
export const getFavoriteHistory = (page = 1, size = 20, userId = 1) => {
  return get('/history/favorites', {
    page,
    size,
    userId
  })
}

/**
 * 切换收藏状态
 * @param {number} historyId - 历史记录ID
 * @param {number} userId - 用户ID
 * @returns {Promise} 操作结果
 */
export const toggleFavorite = (historyId, userId = 1) => {
  return post(`/history/${historyId}/favorite?userId=${userId}`)
}

/**
 * 删除历史记录
 * @param {number} historyId - 历史记录ID
 * @param {number} userId - 用户ID
 * @returns {Promise} 删除结果
 */
export const deleteHistory = (historyId, userId = 1) => {
  return del(`/history/${historyId}`, {
    userId
  })
}

/**
 * 提交用户反馈
 * @param {number} historyId - 历史记录ID
 * @param {number} userId - 用户ID
 * @param {number} rating - 评分 (1-5)
 * @param {string} feedback - 反馈内容
 * @returns {Promise} 提交结果
 */
export const submitFeedback = (historyId, userId = 1, rating, feedback = '') => {
  return post(`/history/${historyId}/feedback?userId=${userId}&rating=${rating}&feedback=${encodeURIComponent(feedback)}`)
}

/**
 * 获取用户使用统计
 * @param {number} userId - 用户ID
 * @returns {Promise} 统计数据
 */
export const getUserStats = (userId = 1) => {
  return get('/history/stats', {
    userId
  })
}

export default {
  getHistoryList,
  getFavoriteHistory,
  toggleFavorite,
  deleteHistory,
  submitFeedback,
  getUserStats
}
