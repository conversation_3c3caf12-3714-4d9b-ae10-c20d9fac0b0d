<template>
  <view class="container">
    <view class="header">
      <text class="title">激活码管理</text>
      <view class="stats">
        <text class="stat-item">总计: {{ totalCount }}</text>
        <text class="stat-item">未使用: {{ unusedCount }}</text>
        <text class="stat-item">已使用: {{ usedCount }}</text>
      </view>
    </view>
    
    <view class="filter-section">
      <view class="filter-row">
        <picker 
          :value="filterStatus" 
          :range="statusOptions" 
          :range-key="'label'"
          @change="onStatusChange"
        >
          <view class="picker">
            状态: {{ statusOptions[filterStatus].label }}
          </view>
        </picker>
        
        <picker 
          :value="filterType" 
          :range="typeOptions" 
          :range-key="'label'"
          @change="onTypeChange"
        >
          <view class="picker">
            类型: {{ typeOptions[filterType].label }}
          </view>
        </picker>
      </view>
      
      <button class="refresh-btn" @click="loadActivationCodes">刷新</button>
    </view>
    
    <view class="list-section">
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>
      
      <view v-else-if="activationCodes.length === 0" class="empty">
        <text>暂无激活码数据</text>
      </view>
      
      <view v-else class="code-list">
        <view 
          v-for="code in activationCodes" 
          :key="code.id" 
          class="code-item"
          @click="showCodeDetail(code)"
        >
          <view class="code-header">
            <text class="code-value">{{ code.code }}</text>
            <view class="status-badge" :class="getStatusClass(code.status)">
              {{ getStatusText(code.status) }}
            </view>
          </view>
          
          <view class="code-info">
            <text class="info-item">类型: {{ getTypeText(code.codeType) }}</text>
            <text class="info-item">时长: {{ code.durationDays }}天</text>
            <text class="info-item">创建: {{ formatDate(code.createdTime) }}</text>
          </view>
          
          <view v-if="code.status === 1" class="usage-info">
            <text class="usage-text">使用者ID: {{ code.usedBy }}</text>
            <text class="usage-text">使用时间: {{ formatDate(code.usedTime) }}</text>
          </view>
          
          <view class="code-actions">
            <button 
              class="copy-btn" 
              @click.stop="copyCode(code.code)"
            >
              复制
            </button>
            <button 
              v-if="code.status === 0" 
              class="disable-btn" 
              @click.stop="disableCode(code)"
            >
              禁用
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActivationCodesManagement',
  
  data() {
    return {
      activationCodes: [],
      loading: false,
      filterStatus: 0,
      filterType: 0,
      statusOptions: [
        { value: -1, label: '全部状态' },
        { value: 0, label: '未使用' },
        { value: 1, label: '已使用' },
        { value: 2, label: '已过期' }
      ],
      typeOptions: [
        { value: '', label: '全部类型' },
        { value: 'premium_3d', label: '3天VIP' },
        { value: 'premium_7d', label: '7天VIP' },
        { value: 'premium_1m', label: '30天VIP' },
        { value: 'premium_3m', label: '90天VIP' },
        { value: 'premium_6m', label: '180天VIP' },
        { value: 'premium_1y', label: '365天VIP' }
      ]
    }
  },
  
  computed: {
    totalCount() {
      return this.activationCodes.length
    },
    
    unusedCount() {
      return this.activationCodes.filter(code => code.status === 0).length
    },
    
    usedCount() {
      return this.activationCodes.filter(code => code.status === 1).length
    }
  },
  
  onLoad() {
    this.loadActivationCodes()
  },
  
  methods: {
    // 加载激活码列表
    async loadActivationCodes() {
      this.loading = true
      
      try {
        // TODO: 调用API获取激活码列表
        // const response = await getActivationCodeList({
        //   status: this.statusOptions[this.filterStatus].value,
        //   codeType: this.typeOptions[this.filterType].value
        // })
        
        // 模拟数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.activationCodes = [
          {
            id: 1,
            code: '8A8Q-P4YT-FFSB-22XS',
            codeType: 'premium_1m',
            durationDays: 30,
            status: 0,
            createdTime: '2025-06-20T23:04:14',
            usedBy: null,
            usedTime: null,
            remark: '管理员yumu生成的30天VIP激活码'
          },
          {
            id: 2,
            code: 'B2C4-D5E6-F7G8-H9I0',
            codeType: 'premium_7d',
            durationDays: 7,
            status: 1,
            createdTime: '2025-06-19T15:30:00',
            usedBy: 5,
            usedTime: '2025-06-20T10:15:30',
            remark: '管理员yumu生成的7天VIP激活码'
          }
        ]
        
      } catch (error) {
        console.error('加载激活码失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 状态筛选改变
    onStatusChange(e) {
      this.filterStatus = e.detail.value
      this.loadActivationCodes()
    },
    
    // 类型筛选改变
    onTypeChange(e) {
      this.filterType = e.detail.value
      this.loadActivationCodes()
    },
    
    // 复制激活码
    copyCode(code) {
      uni.setClipboardData({
        data: code,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
    
    // 禁用激活码
    disableCode(code) {
      uni.showModal({
        title: '确认禁用',
        content: `确定要禁用激活码 ${code.code} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              // TODO: 调用API禁用激活码
              // await disableActivationCode(code.id)
              
              // 模拟禁用
              code.status = 2
              
              uni.showToast({
                title: '禁用成功',
                icon: 'success'
              })
            } catch (error) {
              uni.showToast({
                title: '禁用失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },
    
    // 显示激活码详情
    showCodeDetail(code) {
      const content = [
        `激活码: ${code.code}`,
        `类型: ${this.getTypeText(code.codeType)}`,
        `时长: ${code.durationDays}天`,
        `状态: ${this.getStatusText(code.status)}`,
        `创建时间: ${this.formatDate(code.createdTime)}`,
        code.usedBy ? `使用者ID: ${code.usedBy}` : '',
        code.usedTime ? `使用时间: ${this.formatDate(code.usedTime)}` : '',
        `备注: ${code.remark || '无'}`
      ].filter(Boolean).join('\n')
      
      uni.showModal({
        title: '激活码详情',
        content: content,
        showCancel: false
      })
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case 0: return 'status-unused'
        case 1: return 'status-used'
        case 2: return 'status-expired'
        default: return ''
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 0: return '未使用'
        case 1: return '已使用'
        case 2: return '已过期'
        default: return '未知'
      }
    },
    
    // 获取类型文本
    getTypeText(codeType) {
      const typeMap = {
        'premium_3d': '3天VIP',
        'premium_7d': '7天VIP',
        'premium_1m': '30天VIP',
        'premium_3m': '90天VIP',
        'premium_6m': '180天VIP',
        'premium_1y': '365天VIP'
      }
      return typeMap[codeType] || codeType
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
  padding: 40rpx 30rpx;
  color: white;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .stats {
    display: flex;
    justify-content: space-between;
    
    .stat-item {
      font-size: 24rpx;
      opacity: 0.9;
    }
  }
}

.filter-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .filter-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    
    .picker {
      flex: 1;
      padding: 20rpx;
      background: #f5f5f5;
      border-radius: 8rpx;
      margin: 0 10rpx;
      text-align: center;
      font-size: 28rpx;
    }
  }
  
  .refresh-btn {
    width: 100%;
    height: 80rpx;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
  }
}

.list-section {
  .loading, .empty {
    text-align: center;
    padding: 100rpx 0;
    color: #666;
    font-size: 28rpx;
  }
  
  .code-list {
    .code-item {
      background: white;
      margin: 0 30rpx 20rpx;
      border-radius: 12rpx;
      padding: 30rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      
      .code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;
        
        .code-value {
          font-family: 'Courier New', monospace;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }
        
        .status-badge {
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 22rpx;
          color: white;
          
          &.status-unused {
            background: #52c41a;
          }
          
          &.status-used {
            background: #1890ff;
          }
          
          &.status-expired {
            background: #ff4d4f;
          }
        }
      }
      
      .code-info {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20rpx;
        
        .info-item {
          font-size: 24rpx;
          color: #666;
          margin-right: 30rpx;
          margin-bottom: 10rpx;
        }
      }
      
      .usage-info {
        background: #f0f8ff;
        padding: 15rpx;
        border-radius: 8rpx;
        margin-bottom: 20rpx;
        
        .usage-text {
          display: block;
          font-size: 22rpx;
          color: #1890ff;
          margin-bottom: 5rpx;
        }
      }
      
      .code-actions {
        display: flex;
        justify-content: flex-end;
        
        button {
          padding: 12rpx 24rpx;
          border-radius: 6rpx;
          font-size: 24rpx;
          margin-left: 20rpx;
          border: none;
        }
        
        .copy-btn {
          background: #52c41a;
          color: white;
        }
        
        .disable-btn {
          background: #ff4d4f;
          color: white;
        }
      }
    }
  }
}
</style>
